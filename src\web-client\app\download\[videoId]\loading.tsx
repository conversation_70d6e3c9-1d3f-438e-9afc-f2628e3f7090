import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-muted/20">
        <div className="container mx-auto grid max-w-6xl grid-cols-1 gap-8 px-4 py-8 md:grid-cols-3">
          {/* 左侧区域: 骨架屏 */}
          <aside className="md:col-span-1">
            <div className="sticky top-20 space-y-6">
              <Skeleton className="aspect-video w-full rounded-lg" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-6 w-3/4" />
              </div>
              <Skeleton className="h-8 w-1/2" />
              <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-5/6" />
              </div>
            </div>
          </aside>

          {/* 右侧区域: 骨架屏 */}
          <section className="space-y-6 md:col-span-2">
            <Skeleton className="h-12 w-full" />
            <div className="space-y-4 rounded-lg border p-4">
                 <Skeleton className="h-8 w-full" />
                 <Skeleton className="h-8 w-full" />
                 <Skeleton className="h-8 w-full" />
            </div>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
}