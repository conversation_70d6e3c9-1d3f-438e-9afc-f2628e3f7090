module.exports = [
"[project]/mocks/init.ts [app-rsc] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_4dbd4342._.js",
  "server/chunks/ssr/[root-of-the-server]__62237e79._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/mocks/init.ts [app-rsc] (ecmascript)");
    });
});
}),
];