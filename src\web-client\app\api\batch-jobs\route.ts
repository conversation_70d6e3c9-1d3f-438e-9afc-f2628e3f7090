import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log('🔶 API Route intercepted: POST /api/batch-jobs', body);

    // 返回一个随机的 UUID 作为 batchJobId
    return NextResponse.json({
      batchJobId: crypto.randomUUID(),
    });
  } catch (error) {
    console.error('Error creating batch job:', error);
    return NextResponse.json(
      { error: 'Failed to create batch job' },
      { status: 500 }
    );
  }
}
