module.exports = [
"[project]/.next-internal/server/app/api/youtube/metadata/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/mocks/data/video-page-data.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "mockVideoPageData",
    ()=>mockVideoPageData
]);
const mockVideoPageData = {
    id: 'dQw4w9WgXcQ',
    title: 'Rick Astley - Never Gonna Give You Up (Official Music Video)',
    description: 'The official video for “Never Gonna Give You Up” by Rick Astley...\n' + 'Listen to Rick Astley: https://RickAstley.lnk.to/listenYD\n' + 'Subscribe to the official Rick Astley YouTube channel: https://RickAstley.lnk.to/subscribeYD\n\n' + 'Follow Rick Astley:\n' + 'Facebook: https://RickAstley.lnk.to/followFI\n' + 'Twitter: https://RickAstley.lnk.to/followTI\n' + 'Instagram: https://RickAstley.lnk.to/followII\n' + 'Website: https://RickAstley.lnk.to/followWI\n' + 'Spotify: https://RickAstley.lnk.to/followSI\n' + 'YouTube: https://RickAstley.lnk.to/subscribeYD',
    duration: 212,
    channelName: 'RickAstleyVEVO',
    channelUrl: 'https://www.youtube.com/user/RickAstleyVEVO',
    uploadDate: '2009-10-25T06:57:33Z',
    viewCount: 1589732984,
    commentCount: 2345678,
    commentsDisabled: false,
    thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    thumbnails: [
        {
            format: 'jpg',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
        },
        {
            format: 'png',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png'
        }
    ],
    videoStreams: [
        {
            qualityLabel: '1080p HD',
            resolution: '1920x1080',
            fps: 60,
            fileSize: 98765432,
            format: 'MP4',
            downloadId: 'v1'
        },
        {
            qualityLabel: '720p',
            resolution: '1280x720',
            fps: 30,
            fileSize: 54321098,
            format: 'MP4',
            downloadId: 'v2'
        },
        {
            qualityLabel: '480p',
            resolution: '854x480',
            fps: 30,
            fileSize: 23456789,
            format: 'WebM',
            downloadId: 'v3'
        }
    ],
    audioStreams: [
        {
            qualityLabel: 'High',
            resolution: '',
            fps: 0,
            fileSize: 5678901,
            format: 'M4A',
            bitrate: 128,
            downloadId: 'a1'
        },
        {
            qualityLabel: 'Medium',
            resolution: '',
            fps: 0,
            fileSize: 3456789,
            format: 'WebM',
            bitrate: 160,
            downloadId: 'a2'
        },
        {
            qualityLabel: 'MP3 320kbps',
            resolution: '',
            fps: 0,
            fileSize: 7890123,
            format: 'MP3',
            bitrate: 320,
            downloadId: 'a3-320'
        },
        {
            qualityLabel: 'MP3 192kbps',
            resolution: '',
            fps: 0,
            fileSize: 4567890,
            format: 'MP3',
            bitrate: 192,
            downloadId: 'a3-192'
        }
    ],
    subtitles: [
        {
            langCode: 'en',
            langName: 'English',
            isAutoGenerated: false
        },
        {
            langCode: 'es',
            langName: 'Spanish',
            isAutoGenerated: false
        },
        {
            langCode: 'fr',
            langName: 'French',
            isAutoGenerated: true
        },
        {
            langCode: 'de',
            langName: 'German',
            isAutoGenerated: true
        },
        {
            langCode: 'zh-Hans',
            langName: 'Chinese (Simplified)',
            isAutoGenerated: true
        }
    ]
};
}),
"[project]/app/api/youtube/metadata/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/mocks/data/video-page-data.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('videoId');
    // 模拟网络延迟
    await new Promise((resolve)=>setTimeout(resolve, 500));
    console.log(`🔶 API Route intercepted: /api/youtube/metadata?videoId=${videoId}`);
    if (videoId === 'error') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Video not found'
        }, {
            status: 404
        });
    }
    if (videoId === 'dQw4w9WgXcQ') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockVideoPageData"]);
    }
    // 对于任何其他 videoId，也返回成功的数据
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        ...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockVideoPageData"],
        id: videoId,
        title: `(Mocked) Video Title for ${videoId}`
    });
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__f62416c2._.js.map