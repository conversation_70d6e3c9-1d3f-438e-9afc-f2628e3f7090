{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/video-description.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const VideoDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call VideoDescription() from the server but VideoDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/video-description.tsx <module evaluation>\",\n    \"VideoDescription\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/video-description.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const VideoDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call VideoDescription() from the server but VideoDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/video-description.tsx\",\n    \"VideoDescription\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/comment-downloader.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CommentDownloader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CommentDownloader() from the server but CommentDownloader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/comment-downloader.tsx <module evaluation>\",\n    \"CommentDownloader\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/comment-downloader.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CommentDownloader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CommentDownloader() from the server but CommentDownloader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/comment-downloader.tsx\",\n    \"CommentDownloader\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/download-options-tabs.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DownloadOptionsTabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call DownloadOptionsTabs() from the server but DownloadOptionsTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/download-options-tabs.tsx <module evaluation>\",\n    \"DownloadOptionsTabs\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/download-options-tabs.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DownloadOptionsTabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call DownloadOptionsTabs() from the server but DownloadOptionsTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/download-options-tabs.tsx\",\n    \"DownloadOptionsTabs\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/download/%5BvideoId%5D/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\r\nimport { Footer } from \"@/components/layout/footer\";\r\nimport { AlertTriangle, Home } from \"lucide-react\";\r\nimport { VideoPageData } from \"@/lib/types/definitions\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { VideoDescription } from \"@/components/features/video-description\";\r\nimport { CommentDownloader } from \"@/components/features/comment-downloader\";\r\nimport { DownloadOptionsTabs } from \"@/components/features/download-options-tabs\";\r\n\r\nasync function getVideoData(videoId: string): Promise<VideoPageData | null> {\r\n  try {\r\n    // 在 RSC 中直接使用 fetch，Next.js 会自动处理缓存和去重\r\n    // 我们用 tag 来方便地按需重新验证\r\n    const res = await fetch(`http://localhost:3000/api/youtube/metadata?videoId=${videoId}`, {\r\n      next: { tags: ['video-metadata', videoId] },\r\n    });\r\n\r\n    if (!res.ok) {\r\n      return null;\r\n    }\r\n    return res.json();\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch video data:\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\nexport default async function DownloadPage({\r\n  params,\r\n}: {\r\n  params: Promise<{ videoId: string }>;\r\n}) {\r\n  const { videoId } = await params;\r\n  const videoData = await getVideoData(videoId);\r\n\r\n  // 如果 videoData 为 null，可以渲染一个错误组件或调用 notFound()\r\n  if (!videoData) {\r\n    // return notFound(); // 或者渲染下面的自定义错误组件\r\n    return (\r\n        <div className=\"flex min-h-screen flex-col\">\r\n            <Header />\r\n            <main className=\"flex flex-grow items-center justify-center\">\r\n                <div className=\"flex flex-col items-center gap-4 rounded-lg border p-8 text-center\">\r\n                    <AlertTriangle className=\"h-12 w-12 text-destructive\" />\r\n                    <h1 className=\"text-2xl font-bold\">无法加载此视频</h1>\r\n                    <p className=\"max-w-sm text-muted-foreground\">\r\n                        该视频可能已被删除、设为私有，或存在区域限制。请检查链接是否正确。\r\n                    </p>\r\n                    <Button asChild>\r\n                        <Link href=\"/\">\r\n                            <Home className=\"mr-2 h-4 w-4\" />\r\n                            返回首页\r\n                        </Link>\r\n                    </Button>\r\n                </div>\r\n            </main>\r\n            <Footer />\r\n        </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen flex-col\">\r\n      <Header />\r\n      <main className=\"flex-grow bg-muted/20\">\r\n        <div className=\"container mx-auto grid max-w-6xl grid-cols-1 gap-8 px-4 py-8 md:grid-cols-3\">\r\n          {/* 左侧区域: 视频信息概览 */}\r\n          <aside className=\"md:col-span-1\">\r\n            <div className=\"sticky top-20 space-y-6\">\r\n              {/* 视频缩略图 */}\r\n              <div className=\"space-y-3\">\r\n                <div className=\"overflow-hidden rounded-lg\">\r\n                  <Image\r\n                    src={videoData.thumbnailUrl}\r\n                    alt={`Thumbnail for ${videoData.title}`}\r\n                    width={1280}\r\n                    height={720}\r\n                    className=\"aspect-video w-full object-cover cursor-pointer hover:opacity-90 transition-opacity\"\r\n                    priority\r\n                    onClick={() => {\r\n                      // TODO: 实现弹窗预览功能\r\n                      window.open(videoData.thumbnailUrl, '_blank');\r\n                    }}\r\n                  />\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"flex-1\"\r\n                    onClick={() => {\r\n                      const link = document.createElement('a');\r\n                      link.href = videoData.thumbnailUrl;\r\n                      link.download = `thumbnail-${videoData.id}.jpg`;\r\n                      document.body.appendChild(link);\r\n                      link.click();\r\n                      document.body.removeChild(link);\r\n                    }}\r\n                  >\r\n                    下载缩略图 (JPG)\r\n                  </Button>\r\n                  {videoData.thumbnails.find(t => t.format === 'png') && (\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"flex-1\"\r\n                      onClick={() => {\r\n                        const pngThumbnail = videoData.thumbnails.find(t => t.format === 'png');\r\n                        if (pngThumbnail) {\r\n                          const link = document.createElement('a');\r\n                          link.href = pngThumbnail.url;\r\n                          link.download = `thumbnail-${videoData.id}.png`;\r\n                          document.body.appendChild(link);\r\n                          link.click();\r\n                          document.body.removeChild(link);\r\n                        }\r\n                      }}\r\n                    >\r\n                      下载缩略图 (PNG)\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 核心元数据 */}\r\n              <div className=\"space-y-3\">\r\n                <h1 className=\"text-2xl font-bold leading-tight\" title={videoData.title}>\r\n                  {videoData.title}\r\n                </h1>\r\n                <p className=\"text-sm font-medium text-muted-foreground\">\r\n                  by{' '}\r\n                  <a href={videoData.channelUrl} target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:underline\">\r\n                    {videoData.channelName}\r\n                  </a>\r\n                </p>\r\n                <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n                  <span>{new Date(videoData.uploadDate).toLocaleDateString()}</span>\r\n                  <span>{videoData.viewCount.toLocaleString()} 次观看</span>\r\n                  <span>{videoData.commentCount.toLocaleString()} 条评论</span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 视频描述 */}\r\n              <VideoDescription description={videoData.description} />\r\n              \r\n              {/* 评论下载 */}\r\n              <div className=\"space-y-4 rounded-lg border p-4\">\r\n                 <h3 className=\"font-semibold\">下载评论</h3>\r\n                 <CommentDownloader commentsDisabled={videoData.commentsDisabled} />\r\n              </div>\r\n\r\n            </div>\r\n          </aside>\r\n\r\n          {/* 右侧区域: 下载与处理选项 */}\r\n          <section className=\"md:col-span-2\">\r\n            <DownloadOptionsTabs videoData={videoData} />\r\n          </section>\r\n        </div>\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,eAAe,aAAa,OAAe;IACzC,IAAI;QACF,uCAAuC;QACvC,qBAAqB;QACrB,MAAM,MAAM,MAAM,MAAM,CAAC,mDAAmD,EAAE,SAAS,EAAE;YACvF,MAAM;gBAAE,MAAM;oBAAC;oBAAkB;iBAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,OAAO;QACT;QACA,OAAO,IAAI,IAAI;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAEe,eAAe,aAAa,EACzC,MAAM,EAGP;IACC,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;IAC1B,MAAM,YAAY,MAAM,aAAa;IAErC,+CAA+C;IAC/C,IAAI,CAAC,WAAW;QACd,uCAAuC;QACvC,qBACI,8OAAC;YAAI,WAAU;;8BACX,8OAAC,yIAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACZ,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,yOAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAG9C,8OAAC,qIAAM;gCAAC,OAAO;0CACX,cAAA,8OAAC,uKAAI;oCAAC,MAAK;;sDACP,8OAAC,2MAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAMjD,8OAAC,yIAAM;;;;;;;;;;;IAGjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wIAAK;oDACJ,KAAK,UAAU,YAAY;oDAC3B,KAAK,CAAC,cAAc,EAAE,UAAU,KAAK,EAAE;oDACvC,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,QAAQ;oDACR,SAAS;wDACP,iBAAiB;wDACjB,OAAO,IAAI,CAAC,UAAU,YAAY,EAAE;oDACtC;;;;;;;;;;;0DAGJ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,qIAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,MAAM,OAAO,SAAS,aAAa,CAAC;4DACpC,KAAK,IAAI,GAAG,UAAU,YAAY;4DAClC,KAAK,QAAQ,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC;4DAC/C,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC1B,KAAK,KAAK;4DACV,SAAS,IAAI,CAAC,WAAW,CAAC;wDAC5B;kEACD;;;;;;oDAGA,UAAU,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,wBAC3C,8OAAC,qIAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,MAAM,eAAe,UAAU,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;4DACjE,IAAI,cAAc;gEAChB,MAAM,OAAO,SAAS,aAAa,CAAC;gEACpC,KAAK,IAAI,GAAG,aAAa,GAAG;gEAC5B,KAAK,QAAQ,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,IAAI,CAAC;gEAC/C,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC1B,KAAK,KAAK;gEACV,SAAS,IAAI,CAAC,WAAW,CAAC;4DAC5B;wDACF;kEACD;;;;;;;;;;;;;;;;;;kDAQP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;gDAAmC,OAAO,UAAU,KAAK;0DACpE,UAAU,KAAK;;;;;;0DAElB,8OAAC;gDAAE,WAAU;;oDAA4C;oDACpD;kEACH,8OAAC;wDAAE,MAAM,UAAU,UAAU;wDAAE,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAChF,UAAU,WAAW;;;;;;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB;;;;;;kEACxD,8OAAC;;4DAAM,UAAU,SAAS,CAAC,cAAc;4DAAG;;;;;;;kEAC5C,8OAAC;;4DAAM,UAAU,YAAY,CAAC,cAAc;4DAAG;;;;;;;;;;;;;;;;;;;kDAKnD,8OAAC,mKAAgB;wCAAC,aAAa,UAAU,WAAW;;;;;;kDAGpD,8OAAC;wCAAI,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC,qKAAiB;gDAAC,kBAAkB,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAOtE,8OAAC;4BAAQ,WAAU;sCACjB,cAAA,8OAAC,6KAAmB;gCAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAItC,8OAAC,yIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}