module.exports = [
"[project]/mocks/data/video-page-data.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "mockVideoPageData",
    ()=>mockVideoPageData
]);
const mockVideoPageData = {
    id: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up (Official Music Video)',
    description: 'The official video for “Never Gonna Give You Up” by <PERSON>...\n' + 'Listen to <PERSON>: https://Rick<PERSON>tley.lnk.to/listenYD\n' + 'Subscribe to the official <PERSON> YouTube channel: https://RickAstley.lnk.to/subscribeYD\n\n' + 'Follow <PERSON>:\n' + 'Facebook: https://RickAstley.lnk.to/followFI\n' + 'Twitter: https://RickAs<PERSON>.lnk.to/followTI\n' + 'Instagram: https://<PERSON>As<PERSON>.lnk.to/followII\n' + 'Website: https://RickAs<PERSON>.lnk.to/followWI\n' + 'Spotify: https://RickAstley.lnk.to/followSI\n' + 'YouTube: https://RickAstley.lnk.to/subscribeYD',
    duration: 212,
    channelName: '<PERSON><PERSON>tleyVEVO',
    channelUrl: 'https://www.youtube.com/user/RickAstleyVEVO',
    uploadDate: '2009-10-25T06:57:33Z',
    viewCount: 1589732984,
    commentCount: 2345678,
    commentsDisabled: false,
    thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    thumbnails: [
        {
            format: 'jpg',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
        },
        {
            format: 'png',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png'
        }
    ],
    videoStreams: [
        {
            qualityLabel: '1080p HD',
            resolution: '1920x1080',
            fps: 60,
            fileSize: 98765432,
            format: 'MP4',
            downloadId: 'v1'
        },
        {
            qualityLabel: '720p',
            resolution: '1280x720',
            fps: 30,
            fileSize: 54321098,
            format: 'MP4',
            downloadId: 'v2'
        },
        {
            qualityLabel: '480p',
            resolution: '854x480',
            fps: 30,
            fileSize: 23456789,
            format: 'WebM',
            downloadId: 'v3'
        }
    ],
    audioStreams: [
        {
            qualityLabel: 'High',
            resolution: '',
            fps: 0,
            fileSize: 5678901,
            format: 'M4A',
            bitrate: 128,
            downloadId: 'a1'
        },
        {
            qualityLabel: 'Medium',
            resolution: '',
            fps: 0,
            fileSize: 3456789,
            format: 'WebM',
            bitrate: 160,
            downloadId: 'a2'
        },
        {
            qualityLabel: 'MP3 320kbps',
            resolution: '',
            fps: 0,
            fileSize: 7890123,
            format: 'MP3',
            bitrate: 320,
            downloadId: 'a3-320'
        },
        {
            qualityLabel: 'MP3 192kbps',
            resolution: '',
            fps: 0,
            fileSize: 4567890,
            format: 'MP3',
            bitrate: 192,
            downloadId: 'a3-192'
        }
    ],
    subtitles: [
        {
            langCode: 'en',
            langName: 'English',
            isAutoGenerated: false
        },
        {
            langCode: 'es',
            langName: 'Spanish',
            isAutoGenerated: false
        },
        {
            langCode: 'fr',
            langName: 'French',
            isAutoGenerated: true
        },
        {
            langCode: 'de',
            langName: 'German',
            isAutoGenerated: true
        },
        {
            langCode: 'zh-Hans',
            langName: 'Chinese (Simplified)',
            isAutoGenerated: true
        }
    ]
};
}),
"[project]/mocks/handlers.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "handlers",
    ()=>handlers
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/http.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/HttpResponse.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/delay.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/mocks/data/video-page-data.ts [app-ssr] (ecmascript)");
;
;
;
const handlers = [
    // 示例: 拦截一个 GET 请求
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].get('/api/user', ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            id: 'c7b3d8e0-5e0b-4b0f-8b3a-3b9f4b3d3b3d',
            firstName: 'John',
            lastName: 'Maverick'
        });
    }),
    // 模拟创建批量作业的 API
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].post('/api/batch-jobs', async ()=>{
        // 模拟网络延迟
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delay"])(1500);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            // 返回一个随机的 UUID 作为 batchJobId
            batchJobId: crypto.randomUUID()
        });
    }),
    // 模拟获取视频元数据的 API - 支持服务器组件调用
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].get('http://localhost:3000/api/youtube/metadata', async ({ request })=>{
        const url = new URL(request.url);
        const videoId = url.searchParams.get('videoId');
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delay"])(1000);
        if (videoId === 'error') {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"](null, {
                status: 404,
                statusText: 'Not Found'
            });
        }
        if (videoId === 'dQw4w9WgXcQ') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json(__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockVideoPageData"]);
        }
        // 对于任何其他 videoId，也返回成功的数据
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            ...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockVideoPageData"],
            id: videoId,
            title: `(Mocked) Video Title for ${videoId}`
        });
    }),
    // 同时支持相对路径调用（客户端组件）
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["http"].get('/api/youtube/metadata', async ({ request })=>{
        const url = new URL(request.url);
        const videoId = url.searchParams.get('videoId');
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delay"])(1000);
        if (videoId === 'error') {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"](null, {
                status: 404,
                statusText: 'Not Found'
            });
        }
        if (videoId === 'dQw4w9WgXcQ') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json(__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockVideoPageData"]);
        }
        // 对于任何其他 videoId，也返回成功的数据
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            ...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockVideoPageData"],
            id: videoId,
            title: `(Mocked) Video Title for ${videoId}`
        });
    })
];
}),
"[project]/mocks/browser.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "worker",
    ()=>worker
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$browser$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/browser/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$handlers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/mocks/handlers.ts [app-ssr] (ecmascript)");
;
;
const worker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$browser$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setupWorker"])(...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$handlers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handlers"]);
}),
];

//# sourceMappingURL=mocks_d0cfe784._.js.map