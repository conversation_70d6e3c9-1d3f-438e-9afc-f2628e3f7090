import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Download, Clapperboard, Languages, Scissors } from "lucide-react";

const features = [
  {
    icon: <Download className="h-8 w-8" />,
    title: "高清视频下载",
    description: "支持最高 8K 分辨率，多种格式选择，保留最佳画质与音质。",
  },
  {
    icon: <Clapperboard className="h-8 w-8" />,
    title: "强大批量处理",
    description: "一键处理播放列表或整个频道，自动化完成批量内容获取。",
  },
  {
    icon: <Languages className="h-8 w-8" />,
    title: "独家多语言字幕",
    description: "下载官方、自动生成及双语字幕，满足不同语言学习和观看需求。",
  },
  {
    icon: <Scissors className="h-8 w-8" />,
    title: "在线剪辑与创作",
    description: "无需下载，在线完成视频剪辑、GIF 制作和铃声创作。",
  },
];

export function FeatureCards() {
  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
      {features.map((feature) => (
        <Card key={feature.title}>
          <CardHeader>
            <div className="mb-4 flex justify-center text-primary">
              {feature.icon}
            </div>
            <CardTitle className="text-center">{feature.title}</CardTitle>
            <CardDescription className="text-center">
              {feature.description}
            </CardDescription>
          </CardHeader>
        </Card>
      ))}
    </div>
  );
}