import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const faqs = [
  {
    question: "如何下载 YouTube 播放列表中的所有视频？",
    answer:
      "只需将播放列表的链接粘贴到主页的输入框中，点击搜索。系统会自动识别并引导你进入批量下载页面，你可以在那里选择要下载的视频和全局配置。",
  },
  {
    question: "下载视频是免费的吗？",
    answer:
      "是的，我们提供强大的免费服务。免费版支持下载最高 1080p 的视频、192kbps 的音频，以及批量处理最多 20 个视频。对于需要更高质量或更大规模下载的用户，我们提供专业的 Pro 版本。",
  },
  {
    question: "我可以下载受年龄限制或私有的视频吗？",
    answer:
      "不可以。本平台严格遵守 YouTube 的服务条款，无法访问或下载任何私有视频或需要登录才能观看的受限内容。",
  },
  {
    question: "下载的文件安全吗？",
    answer:
      "绝对安全。所有下载处理都在我们的服务器上完成，你通过安全的签名链接获取文件。我们不会存储你的文件，临时文件也会在固定时间后被自动删除。",
  },
];

export function FaqSection() {
  return (
    <div className="mx-auto max-w-3xl">
      <Accordion type="single" collapsible className="w-full">
        {faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger>{faq.question}</AccordionTrigger>
            <AccordionContent>{faq.answer}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}