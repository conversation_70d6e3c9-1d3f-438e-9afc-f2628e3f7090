'use client';

import { Button } from '@/components/ui/button';
import { VideoPageData } from '@/lib/types/definitions';

interface ThumbnailDownloaderProps {
  videoData: VideoPageData;
}

export function ThumbnailDownloader({ videoData }: ThumbnailDownloaderProps) {
  const downloadThumbnail = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        className="flex-1"
        onClick={() => downloadThumbnail(videoData.thumbnailUrl, `thumbnail-${videoData.id}.jpg`)}
      >
        下载缩略图 (JPG)
      </Button>
      {videoData.thumbnails.find(t => t.format === 'png') && (
        <Button
          variant="outline"
          size="sm"
          className="flex-1"
          onClick={() => {
            const pngThumbnail = videoData.thumbnails.find(t => t.format === 'png');
            if (pngThumbnail) {
              downloadThumbnail(pngThumbnail.url, `thumbnail-${videoData.id}.png`);
            }
          }}
        >
          下载缩略图 (PNG)
        </Button>
      )}
    </div>
  );
}
