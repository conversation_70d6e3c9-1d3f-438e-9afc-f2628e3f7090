{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/video-description.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Copy, Download, Check } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface VideoDescriptionProps {\r\n  description: string;\r\n}\r\n\r\nexport function VideoDescription({ description }: VideoDescriptionProps) {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const [isCopied, setIsCopied] = useState(false);\r\n\r\n  const handleCopyDescription = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(description);\r\n      setIsCopied(true);\r\n      setTimeout(() => setIsCopied(false), 2000);\r\n    } catch (error) {\r\n      console.error('Failed to copy description:', error);\r\n    }\r\n  };\r\n\r\n  const handleDownloadDescription = () => {\r\n    const blob = new Blob([description], { type: 'text/plain;charset=utf-8' });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = 'video-description.txt';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      <div className=\"text-sm\">\r\n        <p\r\n          className={cn('whitespace-pre-wrap', !isExpanded && 'line-clamp-4')}\r\n        >\r\n          {description}\r\n        </p>\r\n        <Button\r\n          variant=\"link\"\r\n          size=\"sm\"\r\n          className=\"px-0\"\r\n          onClick={() => setIsExpanded(!isExpanded)}\r\n        >\r\n          {isExpanded ? '收起' : '展开'}\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={handleCopyDescription}\r\n          disabled={isCopied}\r\n        >\r\n          {isCopied ? (\r\n            <Check className=\"mr-2 h-4 w-4\" />\r\n          ) : (\r\n            <Copy className=\"mr-2 h-4 w-4\" />\r\n          )}\r\n          {isCopied ? '已复制' : '复制描述'}\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={handleDownloadDescription}\r\n        >\r\n          <Download className=\"mr-2 h-4 w-4\" />\r\n          下载描述 (TXT)\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAWO,SAAS,iBAAiB,EAAE,WAAW,EAAyB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IAEzC,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,YAAY;YACZ,WAAW,IAAM,YAAY,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAA2B;QACxE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,IAAA,kHAAE,EAAC,uBAAuB,CAAC,cAAc;kCAEnD;;;;;;kCAEH,8OAAC,qIAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC;kCAE7B,aAAa,OAAO;;;;;;;;;;;;0BAIzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qIAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;4BAET,yBACC,8OAAC,6MAAK;gCAAC,WAAU;;;;;qDAEjB,8OAAC,0MAAI;gCAAC,WAAU;;;;;;4BAEjB,WAAW,QAAQ;;;;;;;kCAEtB,8OAAC,qIAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;;0CAET,8OAAC,sNAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM/C", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,0KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,6KAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,kHAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0KAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,2OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,4KAAsB;kBACrB,cAAA,8OAAC,6KAAuB;YACtB,aAAU;YACV,WAAW,IAAA,kHAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,8KAAwB;oBACvB,WAAW,IAAA,kHAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,0KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mLAA6B;8BAC5B,cAAA,8OAAC,qNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,8KAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,+KAAyB;QACxB,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,oLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,qOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,sLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,2OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/comment-downloader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\n\r\ninterface CommentDownloaderProps {\r\n  commentsDisabled: boolean;\r\n}\r\n\r\nexport function CommentDownloader({ commentsDisabled }: CommentDownloaderProps) {\r\n  if (commentsDisabled) {\r\n    return <p className=\"text-sm text-muted-foreground\">此视频的评论功能已关闭。</p>;\r\n  }\r\n\r\n  // 这里的交互和升级提示等将在后续任务中完善\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"grid grid-cols-2 gap-4\">\r\n        <Select defaultValue=\"hot\">\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"排序方式\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"hot\">热门评论</SelectItem>\r\n            <SelectItem value=\"new\">最新评论</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n        <Select defaultValue=\"100\">\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"下载数量\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"100\">前 100 条</SelectItem>\r\n            <SelectItem value=\"500\" disabled>前 500 条 (Pro)</SelectItem>\r\n            <SelectItem value=\"1000\" disabled>前 1000 条 (Pro)</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n      <div className=\"flex gap-2\">\r\n        <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\r\n          下载 XLSX\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\r\n          下载 CSV\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAeO,SAAS,kBAAkB,EAAE,gBAAgB,EAA0B;IAC5E,IAAI,kBAAkB;QACpB,qBAAO,8OAAC;YAAE,WAAU;sBAAgC;;;;;;IACtD;IAEA,uBAAuB;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qIAAM;wBAAC,cAAa;;0CACnB,8OAAC,4IAAa;0CACZ,cAAA,8OAAC,0IAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,4IAAa;;kDACZ,8OAAC,yIAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,yIAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;kCAG5B,8OAAC,qIAAM;wBAAC,cAAa;;0CACnB,8OAAC,4IAAa;0CACZ,cAAA,8OAAC,0IAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,4IAAa;;kDACZ,8OAAC,yIAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,yIAAU;wCAAC,OAAM;wCAAM,QAAQ;kDAAC;;;;;;kDACjC,8OAAC,yIAAU;wCAAC,OAAM;wCAAO,QAAQ;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAIxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qIAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAS;;;;;;kCAGvD,8OAAC,qIAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAS;;;;;;;;;;;;;;;;;;AAM/D", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,wKAAkB;QACjB,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,wKAAkB;QACjB,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/formatters.ts"], "sourcesContent": ["/**\r\n * 数据格式化相关的工具函数\r\n */\r\n\r\nexport function bytesToSize(bytes: number): string {\r\n  if (bytes === 0) return \"0 Bytes\";\r\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${parseFloat((bytes / Math.pow(1024, i)).toFixed(2))} ${sizes[i]}`;\r\n}"], "names": [], "mappings": "AAAA;;CAEC;;;;AAEM,SAAS,YAAY,KAAa;IACvC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;AAC5E", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/download-options-tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\r\nimport { VideoPageData } from '@/lib/types/definitions';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Download } from 'lucide-react';\r\nimport { bytesToSize } from '@/lib/formatters';\r\n\r\ninterface DownloadOptionsTabsProps {\r\n  videoData: VideoPageData;\r\n}\r\n\r\nexport function DownloadOptionsTabs({ videoData }: DownloadOptionsTabsProps) {\r\n  const { videoStreams, audioStreams, subtitles } = videoData;\r\n\r\n  return (\r\n    <Tabs defaultValue=\"video\">\r\n      <TabsList className=\"grid w-full grid-cols-3\">\r\n        <TabsTrigger value=\"video\">视频</TabsTrigger>\r\n        <TabsTrigger value=\"audio\">音频</TabsTrigger>\r\n        <TabsTrigger value=\"subtitle\">字幕</TabsTrigger>\r\n      </TabsList>\r\n\r\n      {/* 视频选项卡内容 */}\r\n      <TabsContent value=\"video\" className=\"mt-6\">\r\n        <div className=\"space-y-6\">\r\n          {/* 完整视频列表 */}\r\n          <div className=\"rounded-lg border\">\r\n            <div className=\"p-4\">\r\n              <h3 className=\"text-lg font-semibold\">完整视频下载</h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                包含音频的视频文件，按质量从高到低排序。\r\n              </p>\r\n            </div>\r\n            <ul className=\"divide-y border-t\">\r\n              {videoStreams.map((stream) => (\r\n                <li key={stream.downloadId} className=\"flex items-center justify-between p-4\">\r\n                  <div className=\"flex flex-col gap-1\">\r\n                    <span className=\"font-semibold\">{stream.qualityLabel}</span>\r\n                    <span className=\"text-sm text-muted-foreground\">\r\n                      {stream.resolution} • {stream.fps}fps • {stream.format}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-4\">\r\n                    <span className=\"w-24 text-right text-sm font-medium\">\r\n                      {bytesToSize(stream.fileSize)}\r\n                    </span>\r\n                    <Button size=\"sm\">\r\n                      <Download className=\"mr-2 h-4 w-4\" />\r\n                      下载\r\n                    </Button>\r\n                  </div>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n          {/* 视频剪辑面板 (占位) */}\r\n          <div className=\"rounded-lg border p-4\">\r\n            <h3 className=\"text-lg font-semibold\">视频剪辑与 GIF 制作</h3>\r\n            <p className=\"mt-2 text-sm text-muted-foreground\">\r\n              此功能将在后续任务中实现。\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </TabsContent>\r\n\r\n      {/* 音频选项卡内容 */}\r\n      <TabsContent value=\"audio\" className=\"mt-6\">\r\n        <div className=\"space-y-6\">\r\n          {/* 完整音频列表 */}\r\n          <div className=\"rounded-lg border p-4\">\r\n            <h3 className=\"mb-4 text-lg font-semibold\">完整音频下载</h3>\r\n            <ul className=\"space-y-3\">\r\n              {audioStreams.map((stream) => (\r\n                <li\r\n                  key={stream.downloadId}\r\n                  className=\"flex items-center justify-between rounded-lg border p-3\"\r\n                >\r\n                  <div className=\"flex flex-col\">\r\n                    <span className=\"font-medium\">{stream.qualityLabel}</span>\r\n                    <span className=\"text-sm text-muted-foreground\">\r\n                      {stream.format} • {stream.bitrate ? `${stream.bitrate}kbps • ` : ''}\r\n                      {bytesToSize(stream.fileSize)}\r\n                    </span>\r\n                  </div>\r\n                  <Button size=\"sm\">\r\n                    <Download className=\"mr-2 h-4 w-4\" />\r\n                    下载\r\n                  </Button>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n          {/* 音频剪辑面板 (占位) */}\r\n          <div className=\"rounded-lg border p-4\">\r\n            <h3 className=\"text-lg font-semibold\">音频剪辑与铃声制作</h3>\r\n            <p className=\"mt-2 text-sm text-muted-foreground\">\r\n              此功能将在后续任务中实现。\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </TabsContent>\r\n\r\n      {/* 字幕选项卡内容 */}\r\n      <TabsContent value=\"subtitle\" className=\"mt-6\">\r\n        <div className=\"space-y-6\">\r\n          {subtitles.length > 0 ? (\r\n            <>\r\n              {/* 原始字幕列表 */}\r\n              <div className=\"rounded-lg border p-4\">\r\n                <h3 className=\"mb-4 text-lg font-semibold\">原始字幕下载</h3>\r\n                <div className=\"space-y-4\">\r\n                  {/* 官方字幕 */}\r\n                  {subtitles.filter(sub => !sub.isAutoGenerated).length > 0 && (\r\n                    <div>\r\n                      <h4 className=\"mb-2 text-sm font-medium text-muted-foreground\">官方字幕</h4>\r\n                      <div className=\"space-y-2\">\r\n                        {subtitles\r\n                          .filter(sub => !sub.isAutoGenerated)\r\n                          .map((subtitle) => (\r\n                            <div\r\n                              key={subtitle.langCode}\r\n                              className=\"flex items-center justify-between rounded-lg border p-3\"\r\n                            >\r\n                              <span className=\"font-medium\">{subtitle.langName}</span>\r\n                              <div className=\"flex gap-2\">\r\n                                <Button variant=\"outline\" size=\"sm\">\r\n                                  查看\r\n                                </Button>\r\n                                <Button size=\"sm\">\r\n                                  <Download className=\"mr-2 h-4 w-4\" />\r\n                                  下载 SRT\r\n                                </Button>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* 自动生成字幕 */}\r\n                  {subtitles.filter(sub => sub.isAutoGenerated).length > 0 && (\r\n                    <div>\r\n                      <h4 className=\"mb-2 text-sm font-medium text-muted-foreground\">自动生成字幕</h4>\r\n                      <div className=\"space-y-2\">\r\n                        {subtitles\r\n                          .filter(sub => sub.isAutoGenerated)\r\n                          .map((subtitle) => (\r\n                            <div\r\n                              key={subtitle.langCode}\r\n                              className=\"flex items-center justify-between rounded-lg border p-3\"\r\n                            >\r\n                              <span className=\"font-medium\">{subtitle.langName}</span>\r\n                              <div className=\"flex gap-2\">\r\n                                <Button variant=\"outline\" size=\"sm\">\r\n                                  查看\r\n                                </Button>\r\n                                <Button size=\"sm\">\r\n                                  <Download className=\"mr-2 h-4 w-4\" />\r\n                                  下载 SRT\r\n                                </Button>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <Button variant=\"outline\" className=\"w-full\">\r\n                    <Download className=\"mr-2 h-4 w-4\" />\r\n                    一键下载所有原始字幕 (.zip)\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 多语言字幕制作面板 (占位) */}\r\n              <div className=\"rounded-lg border p-4\">\r\n                <h3 className=\"text-lg font-semibold\">多语言字幕制作</h3>\r\n                <p className=\"mt-2 text-sm text-muted-foreground\">\r\n                  此功能将在后续任务中实现。\r\n                </p>\r\n              </div>\r\n            </>\r\n          ) : (\r\n            <div className=\"rounded-lg border p-8 text-center\">\r\n              <p className=\"text-muted-foreground\">此视频没有任何可用字幕</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </TabsContent>\r\n    </Tabs>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAYO,SAAS,oBAAoB,EAAE,SAAS,EAA4B;IACzE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;IAElD,qBACE,8OAAC,iIAAI;QAAC,cAAa;;0BACjB,8OAAC,qIAAQ;gBAAC,WAAU;;kCAClB,8OAAC,wIAAW;wBAAC,OAAM;kCAAQ;;;;;;kCAC3B,8OAAC,wIAAW;wBAAC,OAAM;kCAAQ;;;;;;kCAC3B,8OAAC,wIAAW;wBAAC,OAAM;kCAAW;;;;;;;;;;;;0BAIhC,8OAAC,wIAAW;gBAAC,OAAM;gBAAQ,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;4CAA2B,WAAU;;8DACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAiB,OAAO,YAAY;;;;;;sEACpD,8OAAC;4DAAK,WAAU;;gEACb,OAAO,UAAU;gEAAC;gEAAI,OAAO,GAAG;gEAAC;gEAAO,OAAO,MAAM;;;;;;;;;;;;;8DAG1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,IAAA,gIAAW,EAAC,OAAO,QAAQ;;;;;;sEAE9B,8OAAC,qIAAM;4DAAC,MAAK;;8EACX,8OAAC,sNAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;2CAZlC,OAAO,UAAU;;;;;;;;;;;;;;;;sCAqBhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,wIAAW;gBAAC,OAAM;gBAAQ,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAe,OAAO,YAAY;;;;;;sEAClD,8OAAC;4DAAK,WAAU;;gEACb,OAAO,MAAM;gEAAC;gEAAI,OAAO,OAAO,GAAG,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG;gEAChE,IAAA,gIAAW,EAAC,OAAO,QAAQ;;;;;;;;;;;;;8DAGhC,8OAAC,qIAAM;oDAAC,MAAK;;sEACX,8OAAC,sNAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;2CAXlC,OAAO,UAAU;;;;;;;;;;;;;;;;sCAmB9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,wIAAW;gBAAC,OAAM;gBAAW,WAAU;0BACtC,cAAA,8OAAC;oBAAI,WAAU;8BACZ,UAAU,MAAM,GAAG,kBAClB;;0CAEE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;4CAEZ,UAAU,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,eAAe,EAAE,MAAM,GAAG,mBACtD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEACZ,UACE,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,eAAe,EAClC,GAAG,CAAC,CAAC,yBACJ,8OAAC;gEAEC,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAe,SAAS,QAAQ;;;;;;kFAChD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,qIAAM;gFAAC,SAAQ;gFAAU,MAAK;0FAAK;;;;;;0FAGpC,8OAAC,qIAAM;gFAAC,MAAK;;kGACX,8OAAC,sNAAQ;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;+DATpC,SAAS,QAAQ;;;;;;;;;;;;;;;;4CAoBjC,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,eAAe,EAAE,MAAM,GAAG,mBACrD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEACZ,UACE,MAAM,CAAC,CAAA,MAAO,IAAI,eAAe,EACjC,GAAG,CAAC,CAAC,yBACJ,8OAAC;gEAEC,WAAU;;kFAEV,8OAAC;wEAAK,WAAU;kFAAe,SAAS,QAAQ;;;;;;kFAChD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,qIAAM;gFAAC,SAAQ;gFAAU,MAAK;0FAAK;;;;;;0FAGpC,8OAAC,qIAAM;gFAAC,MAAK;;kGACX,8OAAC,sNAAQ;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;+DATpC,SAAS,QAAQ;;;;;;;;;;;;;;;;0DAmBlC,8OAAC,qIAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,8OAAC,sNAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;qDAMtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}