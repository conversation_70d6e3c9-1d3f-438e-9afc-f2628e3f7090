{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/shared/msw-component.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\n\r\nexport function MSWComponent() {\r\n  useEffect(() => {\r\n    async function initMSW() {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        const { worker } = await import('@/mocks/browser');\r\n        await worker.start({\r\n          onUnhandledRequest: 'bypass',\r\n        });\r\n        console.log('🔶 MSW worker started for browser (client-side only)');\r\n      }\r\n    }\r\n    initMSW();\r\n  }, []);\r\n\r\n  return null;\r\n}"], "names": [], "mappings": ";;;;AAOU;AALV;;AAFA;;AAIO,SAAS;;IACd,IAAA,0KAAS;kCAAC;YACR,eAAe;gBACb,wCAA4C;oBAC1C,MAAM,EAAE,MAAM,EAAE,GAAG;oBACnB,MAAM,OAAO,KAAK,CAAC;wBACjB,oBAAoB;oBACtB;oBACA,QAAQ,GAAG,CAAC;gBACd;YACF;YACA;QACF;iCAAG,EAAE;IAEL,OAAO;AACT;GAfgB;KAAA", "debugId": null}}]}