{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/header.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container mx-auto flex h-14 items-center px-4\">\r\n        <div className=\"flex flex-1 items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n              <DownloadCloud className=\"h-6 w-6\" />\r\n              <span className=\"font-bold sm:inline-block\">YTDownloader</span>\r\n            </Link>\r\n          </div>\r\n          <nav className=\"hidden items-center space-x-6 text-sm md:flex\">\r\n            <Link\r\n              href=\"/#features\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              功能\r\n            </Link>\r\n            <Link\r\n              href=\"/pricing\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              价格\r\n            </Link>\r\n            <Link\r\n              href=\"/#faq\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              FAQ\r\n            </Link>\r\n            <div className=\"w-px h-6 bg-border\" />\r\n            <Button variant=\"ghost\">登录</Button>\r\n            <Button>注册</Button>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC,qIAAM;gCAAC,SAAQ;0CAAQ;;;;;;0CACxB,8OAAC,qIAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Footer() {\r\n  return (\r\n    <footer className=\"border-t\">\r\n      <div className=\"container mx-auto space-y-4 py-8 px-4 text-center md:flex md:items-center md:justify-between md:space-y-0 md:py-4\">\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          <DownloadCloud className=\"h-5 w-5\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            &copy; {new Date().getFullYear()} YTDownloader. All rights reserved.\r\n          </span>\r\n        </div>\r\n        <div className=\"text-center text-sm text-muted-foreground md:text-left\">\r\n          <p>\r\n            免责声明: 本服务仅供个人学习和研究使用，请勿用于商业用途。用户需自行承担因使用本服务而产生的版权风险。\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center justify-center space-x-4 text-sm font-medium text-muted-foreground\">\r\n          <Link href=\"/terms\" className=\"hover:text-foreground\">\r\n            服务条款\r\n          </Link>\r\n          <Link href=\"/privacy\" className=\"hover:text-foreground\">\r\n            隐私政策\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yOAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;;gCAAgC;gCACtC,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;8BAIL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAwB;;;;;;sCAGtD,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/terms/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\nimport { Footer } from \"@/components/layout/footer\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n\nexport const metadata = {\n  title: \"服务条款 - YTDownloader\",\n  description: \"YTDownloader 服务条款和使用协议\",\n};\n\nexport default function TermsPage() {\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <Header />\n      \n      <main className=\"flex-grow bg-muted/20\">\n        <div className=\"container mx-auto max-w-4xl px-4 py-8\">\n          <div className=\"mb-8 text-center\">\n            <h1 className=\"text-4xl font-bold\">服务条款</h1>\n            <p className=\"mt-4 text-lg text-muted-foreground\">\n              最后更新时间：{new Date().toLocaleDateString('zh-CN')}\n            </p>\n          </div>\n\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>1. 服务说明</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  YTDownloader是一个专业的YouTube内容下载平台，\n                  为用户提供视频、音频、字幕等内容的下载服务。\n                </p>\n                <p>\n                  本服务支持单次下载和批量处理，并提供在线剪辑与格式转换功能。\n                  我们致力于为用户提供高质量、高效率的下载体验。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>2. 用户责任</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  <strong>2.1 合法使用：</strong>\n                  用户承诺仅将本服务用于个人学习、研究等非商业用途。\n                  严禁将下载的内容用于任何商业目的或侵犯他人版权的行为。\n                </p>\n                <p>\n                  <strong>2.2 版权责任：</strong>\n                  用户需自行承担因使用本服务而产生的版权风险。\n                  本服务不对用户下载的内容承担任何版权责任。\n                </p>\n                <p>\n                  <strong>2.3 禁止行为：</strong>\n                  用户不得利用本服务进行任何违法违规活动，包括但不限于：\n                  传播违法内容、侵犯他人权益、恶意攻击服务器等。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>3. 服务限制</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  <strong>3.1 免费版限制：</strong>\n                  免费用户可下载最高1080p分辨率视频、192kbps音频，\n                  批量处理最多20个视频，评论下载限100条。\n                </p>\n                <p>\n                  <strong>3.2 专业版服务：</strong>\n                  付费用户可享受更高质量下载、更大批量处理规模等增值服务。\n                </p>\n                <p>\n                  <strong>3.3 技术限制：</strong>\n                  本服务无法下载私有视频、受年龄限制的内容或需要登录才能观看的视频。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>4. 隐私保护</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  我们重视用户隐私，严格按照《隐私政策》处理用户数据。\n                  详细信息请参阅我们的隐私政策页面。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>5. 免责声明</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  <strong>5.1 服务可用性：</strong>\n                  本服务力求保持稳定运行，但不保证服务的绝对可用性。\n                  因网络故障、维护升级等原因导致的服务中断，本服务不承担责任。\n                </p>\n                <p>\n                  <strong>5.2 内容准确性：</strong>\n                  本服务不对下载内容的准确性、完整性或适用性作出保证。\n                </p>\n                <p>\n                  <strong>5.3 第三方风险：</strong>\n                  本服务不对因使用第三方平台（如YouTube）政策变更而导致的服务影响承担责任。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>6. 条款变更</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  本服务保留随时修改本服务条款的权利。\n                  条款变更后，我们将在网站上发布更新通知。\n                  用户继续使用本服务即表示同意修改后的条款。\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>7. 联系我们</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <p>\n                  如果您对本服务条款有任何疑问，请通过以下方式联系我们：\n                </p>\n                <ul className=\"list-disc list-inside space-y-1\">\n                  <li>邮箱：<EMAIL></li>\n                  <li>在线客服：通过网站右下角客服窗口</li>\n                </ul>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;;wCAAqC;wCACxC,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC;8DAAE;;;;;;8DAIH,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;8CAOP,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;8DAI5B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;8DAI5B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAI7B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAG7B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;;;;;;;;;;;;;8CAMhC,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAOP,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAI7B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAG7B,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;;;;;;;;;;;;;8CAMjC,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAQP,8OAAC,iIAAI;;sDACH,8OAAC,uIAAU;sDACT,cAAA,8OAAC,sIAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC;8DAAE;;;;;;8DAGH,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC,yIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}