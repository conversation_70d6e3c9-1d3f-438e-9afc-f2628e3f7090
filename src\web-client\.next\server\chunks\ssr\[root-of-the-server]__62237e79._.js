module.exports = [
"[project]/mocks/env-setup.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * MSW 环境设置工具
 * 确保在正确的环境中初始化正确的MSW实例
 */ __turbopack_context__.s([
    "isClient",
    ()=>isClient,
    "isDevelopment",
    ()=>isDevelopment,
    "isServer",
    ()=>isServer,
    "logEnvironmentInfo",
    ()=>logEnvironmentInfo,
    "shouldInitializeClientMSW",
    ()=>shouldInitializeClientMSW,
    "shouldInitializeServerMSW",
    ()=>shouldInitializeServerMSW
]);
function isServer() {
    return "undefined" === 'undefined';
}
function isClient() {
    return "undefined" !== 'undefined';
}
function isDevelopment() {
    return ("TURBOPACK compile-time value", "development") === 'development';
}
function shouldInitializeServerMSW() {
    return isDevelopment() && isServer();
}
function shouldInitializeClientMSW() {
    return isDevelopment() && isClient();
}
function logEnvironmentInfo() {
    console.log('🔍 MSW Environment Info:', {
        isDevelopment: isDevelopment(),
        isServer: isServer(),
        isClient: isClient(),
        shouldInitServer: shouldInitializeServerMSW(),
        shouldInitClient: shouldInitializeClientMSW()
    });
}
}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/net [external] (net, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}),
"[externals]/_http_common [external] (_http_common, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("_http_common", () => require("_http_common"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[project]/mocks/data/video-page-data.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "mockVideoPageData",
    ()=>mockVideoPageData
]);
const mockVideoPageData = {
    id: 'dQw4w9WgXcQ',
    title: 'Rick Astley - Never Gonna Give You Up (Official Music Video)',
    description: 'The official video for “Never Gonna Give You Up” by Rick Astley...\n' + 'Listen to Rick Astley: https://RickAstley.lnk.to/listenYD\n' + 'Subscribe to the official Rick Astley YouTube channel: https://RickAstley.lnk.to/subscribeYD\n\n' + 'Follow Rick Astley:\n' + 'Facebook: https://RickAstley.lnk.to/followFI\n' + 'Twitter: https://RickAstley.lnk.to/followTI\n' + 'Instagram: https://RickAstley.lnk.to/followII\n' + 'Website: https://RickAstley.lnk.to/followWI\n' + 'Spotify: https://RickAstley.lnk.to/followSI\n' + 'YouTube: https://RickAstley.lnk.to/subscribeYD',
    duration: 212,
    channelName: 'RickAstleyVEVO',
    channelUrl: 'https://www.youtube.com/user/RickAstleyVEVO',
    uploadDate: '2009-10-25T06:57:33Z',
    viewCount: 1589732984,
    commentCount: 2345678,
    commentsDisabled: false,
    thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    thumbnails: [
        {
            format: 'jpg',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
        },
        {
            format: 'png',
            url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png'
        }
    ],
    videoStreams: [
        {
            qualityLabel: '1080p HD',
            resolution: '1920x1080',
            fps: 60,
            fileSize: 98765432,
            format: 'MP4',
            downloadId: 'v1'
        },
        {
            qualityLabel: '720p',
            resolution: '1280x720',
            fps: 30,
            fileSize: 54321098,
            format: 'MP4',
            downloadId: 'v2'
        },
        {
            qualityLabel: '480p',
            resolution: '854x480',
            fps: 30,
            fileSize: 23456789,
            format: 'WebM',
            downloadId: 'v3'
        }
    ],
    audioStreams: [
        {
            qualityLabel: 'High',
            resolution: '',
            fps: 0,
            fileSize: 5678901,
            format: 'M4A',
            bitrate: 128,
            downloadId: 'a1'
        },
        {
            qualityLabel: 'Medium',
            resolution: '',
            fps: 0,
            fileSize: 3456789,
            format: 'WebM',
            bitrate: 160,
            downloadId: 'a2'
        },
        {
            qualityLabel: 'MP3 320kbps',
            resolution: '',
            fps: 0,
            fileSize: 7890123,
            format: 'MP3',
            bitrate: 320,
            downloadId: 'a3-320'
        },
        {
            qualityLabel: 'MP3 192kbps',
            resolution: '',
            fps: 0,
            fileSize: 4567890,
            format: 'MP3',
            bitrate: 192,
            downloadId: 'a3-192'
        }
    ],
    subtitles: [
        {
            langCode: 'en',
            langName: 'English',
            isAutoGenerated: false
        },
        {
            langCode: 'es',
            langName: 'Spanish',
            isAutoGenerated: false
        },
        {
            langCode: 'fr',
            langName: 'French',
            isAutoGenerated: true
        },
        {
            langCode: 'de',
            langName: 'German',
            isAutoGenerated: true
        },
        {
            langCode: 'zh-Hans',
            langName: 'Chinese (Simplified)',
            isAutoGenerated: true
        }
    ]
};
}),
"[project]/mocks/handlers.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "handlers",
    ()=>handlers
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/http.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/HttpResponse.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/msw/lib/core/delay.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/mocks/data/video-page-data.ts [app-rsc] (ecmascript)");
;
;
;
const handlers = [
    // 示例: 拦截一个 GET 请求
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["http"].get('/api/user', ()=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            id: 'c7b3d8e0-5e0b-4b0f-8b3a-3b9f4b3d3b3d',
            firstName: 'John',
            lastName: 'Maverick'
        });
    }),
    // 模拟获取视频元数据的 API - 支持服务器组件和客户端组件调用
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["http"].get('http://localhost:3000/api/youtube/metadata', async ({ request })=>{
        const url = new URL(request.url);
        const videoId = url.searchParams.get('videoId');
        console.log(`🔶 MSW intercepted (absolute): ${request.url}`);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["delay"])(500);
        if (videoId === 'error') {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"](null, {
                status: 404,
                statusText: 'Not Found'
            });
        }
        if (videoId === 'dQw4w9WgXcQ') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json(__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mockVideoPageData"]);
        }
        // 对于任何其他 videoId，也返回成功的数据
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            ...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mockVideoPageData"],
            id: videoId,
            title: `(Mocked) Video Title for ${videoId}`
        });
    }),
    // 同时支持相对路径调用（客户端组件）
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["http"].get('/api/youtube/metadata', async ({ request })=>{
        const url = new URL(request.url);
        const videoId = url.searchParams.get('videoId');
        console.log(`🔶 MSW intercepted (relative): ${request.url}`);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["delay"])(500);
        if (videoId === 'error') {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"](null, {
                status: 404,
                statusText: 'Not Found'
            });
        }
        if (videoId === 'dQw4w9WgXcQ') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json(__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mockVideoPageData"]);
        }
        // 对于任何其他 videoId，也返回成功的数据
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            ...__TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$data$2f$video$2d$page$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mockVideoPageData"],
            id: videoId,
            title: `(Mocked) Video Title for ${videoId}`
        });
    }),
    // 模拟创建批量作业的 API - 绝对路径（服务器组件）
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["http"].post('http://localhost:3000/api/batch-jobs', async ({ request })=>{
        console.log(`🔶 MSW intercepted (absolute): POST ${request.url}`);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["delay"])(1500);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            batchJobId: crypto.randomUUID()
        });
    }),
    // 模拟创建批量作业的 API - 相对路径（客户端组件）
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$http$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["http"].post('/api/batch-jobs', async ({ request })=>{
        console.log(`🔶 MSW intercepted (relative): POST ${request.url}`);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$delay$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["delay"])(1500);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$msw$2f$lib$2f$core$2f$HttpResponse$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HttpResponse"].json({
            batchJobId: crypto.randomUUID()
        });
    })
];
}),
"[project]/mocks/init.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * MSW 初始化文件
 * 在开发环境中启动模拟服务器
 */ __turbopack_context__.s([]);
var __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$env$2d$setup$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/mocks/env-setup.ts [app-rsc] (ecmascript)");
;
// 只在应该初始化服务器端MSW时执行
if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$env$2d$setup$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shouldInitializeServerMSW"])()) {
    // 防止重复初始化
    if (!global.__MSW_SERVER_INITIALIZED__) {
        global.__MSW_SERVER_INITIALIZED__ = true;
        // 记录环境信息
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$mocks$2f$env$2d$setup$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logEnvironmentInfo"])();
        // 使用同步require以确保在Next.js启动时立即初始化
        try {
            const { setupServer } = __turbopack_context__.r("[project]/node_modules/msw/lib/node/index.mjs [app-rsc] (ecmascript)");
            const { handlers } = __turbopack_context__.r("[project]/mocks/handlers.ts [app-rsc] (ecmascript)");
            const server = setupServer(...handlers);
            server.listen({
                onUnhandledRequest: 'bypass'
            });
            console.log('🔶 MSW server started for Node.js (server-side)');
            // 确保在进程退出时关闭服务器
            const cleanup = ()=>{
                server.close();
                console.log('🔶 MSW server stopped');
            };
            process.on('SIGINT', cleanup);
            process.on('SIGTERM', cleanup);
            process.on('exit', cleanup);
        } catch (error) {
            console.error('❌ Failed to initialize MSW server:', error);
        }
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__62237e79._.js.map