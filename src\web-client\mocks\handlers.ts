import { http, HttpResponse } from 'msw';
import { delay } from 'msw';
import { mockVideoPageData } from './data/video-page-data';

export const handlers = [
  // 示例: 拦截一个 GET 请求
  http.get('/api/user', () => {
    return HttpResponse.json({
      id: 'c7b3d8e0-5e0b-4b0f-8b3a-3b9f4b3d3b3d',
      firstName: 'John',
      lastName: 'Maverick',
    });
  }),

  // 模拟创建批量作业的 API
  http.post('/api/batch-jobs', async () => {
    // 模拟网络延迟
    await delay(1500);

    return HttpResponse.json({
      // 返回一个随机的 UUID 作为 batchJobId
      batchJobId: crypto.randomUUID(),
    });
  }),

  // 模拟获取视频元数据的 API - 支持服务器组件调用
  http.get('http://localhost:3000/api/youtube/metadata', async ({ request }) => {
    const url = new URL(request.url);
    const videoId = url.searchParams.get('videoId');

    await delay(1000);

    if (videoId === 'error') {
      return new HttpResponse(null, { status: 404, statusText: 'Not Found' });
    }

    if (videoId === 'dQw4w9WgXcQ') {
      return HttpResponse.json(mockVideoPageData);
    }

    // 对于任何其他 videoId，也返回成功的数据
    return HttpResponse.json({
      ...mockVideoPageData,
      id: videoId,
      title: `(Mocked) Video Title for ${videoId}`,
    });
  }),

  // 同时支持相对路径调用（客户端组件）
  http.get('/api/youtube/metadata', async ({ request }) => {
    const url = new URL(request.url);
    const videoId = url.searchParams.get('videoId');

    await delay(1000);

    if (videoId === 'error') {
      return new HttpResponse(null, { status: 404, statusText: 'Not Found' });
    }

    if (videoId === 'dQw4w9WgXcQ') {
      return HttpResponse.json(mockVideoPageData);
    }

    // 对于任何其他 videoId，也返回成功的数据
    return HttpResponse.json({
      ...mockVideoPageData,
      id: videoId,
      title: `(Mocked) Video Title for ${videoId}`,
    });
  }),
];