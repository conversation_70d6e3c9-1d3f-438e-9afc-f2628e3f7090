(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/mocks/browser.ts [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_2668fdef._.js",
  "static/chunks/mocks_391a1b32._.js",
  "static/chunks/mocks_browser_ts_3ec10e30._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/mocks/browser.ts [app-client] (ecmascript)");
    });
});
}),
]);