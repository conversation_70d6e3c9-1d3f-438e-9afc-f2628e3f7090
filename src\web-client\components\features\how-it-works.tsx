import { Card, CardContent } from "@/components/ui/card";
import { Co<PERSON>, <PERSON>ting<PERSON>, ArrowDownToLine } from "lucide-react";

const steps = [
  {
    icon: <Copy className="h-10 w-10" />,
    title: "1. 粘贴链接",
    description: "将任何 YouTube 视频、播放列表或频道链接粘贴到输入框中。",
  },
  {
    icon: <Settings className="h-10 w-10" />,
    title: "2. 配置选项",
    description: "选择你需要的格式、分辨率、字幕语言或其他高级处理选项。",
  },
  {
    icon: <ArrowDownToLine className="h-10 w-10" />,
    title: "3. 开始下载",
    description: "点击下载按钮，获取你的内容。批量任务将在后台自动完成。",
  },
];

export function HowItWorks() {
  return (
    <div className="mx-auto grid max-w-5xl grid-cols-1 items-center gap-8 md:grid-cols-3 md:gap-16">
      {steps.map((step, index) => (
        <div key={step.title} className="relative flex flex-col items-center">
          <Card className="w-full">
            <CardContent className="flex flex-col items-center gap-4 p-8">
              <div className="mb-4 rounded-full bg-primary/10 p-4 text-primary">
                {step.icon}
              </div>
              <h3 className="text-xl font-bold">{step.title}</h3>
              <p className="text-center text-muted-foreground">
                {step.description}
              </p>
            </CardContent>
          </Card>
          {index < steps.length - 1 && (
            <div className="absolute right-0 top-1/2 hidden translate-x-1/2 transform md:block">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}