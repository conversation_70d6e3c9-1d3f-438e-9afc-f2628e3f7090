import { VideoPageData } from '@/lib/types/definitions';

/**
 * 单次下载页的模拟数据
 * videoId: dQw4w9WgXcQ (<PERSON> - Never Gonna Give You Up)
 */
export const mockVideoPageData: VideoPageData = {
  id: 'dQw4w9WgXcQ',
  title: '<PERSON> - Never Gonna Give You Up (Official Music Video)',
  description:
    'The official video for “Never Gonna Give You Up” by <PERSON>...\n' +
    'Listen to <PERSON>: https://RickAs<PERSON>.lnk.to/listenYD\n' +
    'Subscribe to the official <PERSON> YouTube channel: https://RickAstley.lnk.to/subscribeYD\n\n' +
    'Follow <PERSON>:\n' +
    'Facebook: https://RickAstley.lnk.to/followFI\n' +
    'Twitter: https://RickAstley.lnk.to/followTI\n' +
    'Instagram: https://RickAstley.lnk.to/followII\n' +
    'Website: https://RickAstley.lnk.to/followWI\n' +
    'Spotify: https://RickAstley.lnk.to/followSI\n' +
    'YouTube: https://RickAstley.lnk.to/subscribeYD',
  duration: 212,
  channelName: 'RickAstleyVEVO',
  channelUrl: 'https://www.youtube.com/user/<PERSON>tleyVEVO',
  uploadDate: '2009-10-25T06:57:33Z',
  viewCount: 1589732984,
  commentCount: 2345678,
  commentsDisabled: false,
  thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
  thumbnails: [
    { format: 'jpg', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg' },
    { format: 'png', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png' }, // 示例, png 通常不存在
  ],
  videoStreams: [
    { qualityLabel: '1080p HD', resolution: '1920x1080', fps: 60, fileSize: 98765432, format: 'MP4', downloadId: 'v1' },
    { qualityLabel: '720p', resolution: '1280x720', fps: 30, fileSize: 54321098, format: 'MP4', downloadId: 'v2' },
    { qualityLabel: '480p', resolution: '854x480', fps: 30, fileSize: 23456789, format: 'WebM', downloadId: 'v3' },
  ],
  audioStreams: [
    { qualityLabel: 'High', resolution: '', fps: 0, fileSize: 5678901, format: 'M4A', bitrate: 128, downloadId: 'a1' },
    { qualityLabel: 'Medium', resolution: '', fps: 0, fileSize: 3456789, format: 'WebM', bitrate: 160, downloadId: 'a2' },
    { qualityLabel: 'MP3 320kbps', resolution: '', fps: 0, fileSize: 7890123, format: 'MP3', bitrate: 320, downloadId: 'a3-320' },
    { qualityLabel: 'MP3 192kbps', resolution: '', fps: 0, fileSize: 4567890, format: 'MP3', bitrate: 192, downloadId: 'a3-192' },
  ],
  subtitles: [
    { langCode: 'en', langName: 'English', isAutoGenerated: false },
    { langCode: 'es', langName: 'Spanish', isAutoGenerated: false },
    { langCode: 'fr', langName: 'French', isAutoGenerated: true },
    { langCode: 'de', langName: 'German', isAutoGenerated: true },
    { langCode: 'zh-Hans', langName: 'Chinese (Simplified)', isAutoGenerated: true },
  ],
};