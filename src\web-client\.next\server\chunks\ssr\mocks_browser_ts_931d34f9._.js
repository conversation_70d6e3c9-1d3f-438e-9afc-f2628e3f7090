module.exports = [
"[project]/mocks/browser.ts [app-ssr] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_66c53109._.js",
  "server/chunks/ssr/mocks_318be026._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/mocks/browser.ts [app-ssr] (ecmascript)");
    });
});
}),
];