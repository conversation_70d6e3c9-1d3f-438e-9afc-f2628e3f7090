{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/header.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container mx-auto flex h-14 items-center px-4\">\r\n        <div className=\"flex flex-1 items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n              <DownloadCloud className=\"h-6 w-6\" />\r\n              <span className=\"font-bold sm:inline-block\">YTDownloader</span>\r\n            </Link>\r\n          </div>\r\n          <nav className=\"hidden items-center space-x-6 text-sm md:flex\">\r\n            <Link\r\n              href=\"/#features\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              功能\r\n            </Link>\r\n            <Link\r\n              href=\"/pricing\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              价格\r\n            </Link>\r\n            <Link\r\n              href=\"/#faq\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              FAQ\r\n            </Link>\r\n            <div className=\"w-px h-6 bg-border\" />\r\n            <Button variant=\"ghost\">登录</Button>\r\n            <Button>注册</Button>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC,qIAAM;gCAAC,SAAQ;0CAAQ;;;;;;0CACxB,8OAAC,qIAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Footer() {\r\n  return (\r\n    <footer className=\"border-t\">\r\n      <div className=\"container mx-auto space-y-4 py-8 px-4 text-center md:flex md:items-center md:justify-between md:space-y-0 md:py-4\">\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          <DownloadCloud className=\"h-5 w-5\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            &copy; {new Date().getFullYear()} YTDownloader. All rights reserved.\r\n          </span>\r\n        </div>\r\n        <div className=\"text-center text-sm text-muted-foreground md:text-left\">\r\n          <p>\r\n            免责声明: 本服务仅供个人学习和研究使用，请勿用于商业用途。用户需自行承担因使用本服务而产生的版权风险。\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center justify-center space-x-4 text-sm font-medium text-muted-foreground\">\r\n          <Link href=\"/terms\" className=\"hover:text-foreground\">\r\n            服务条款\r\n          </Link>\r\n          <Link href=\"/privacy\" className=\"hover:text-foreground\">\r\n            隐私政策\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yOAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;;gCAAgC;gCACtC,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;8BAIL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAwB;;;;;;sCAGtD,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/link-input-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LinkInputForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LinkInputForm() from the server but LinkInputForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/link-input-form.tsx <module evaluation>\",\n    \"LinkInputForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/link-input-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LinkInputForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LinkInputForm() from the server but LinkInputForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/features/link-input-form.tsx\",\n    \"LinkInputForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/feature-cards.tsx"], "sourcesContent": ["import { Card, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\r\nimport { Download, Clapperboard, Languages, Scissors } from \"lucide-react\";\r\n\r\nconst features = [\r\n  {\r\n    icon: <Download className=\"h-8 w-8\" />,\r\n    title: \"高清视频下载\",\r\n    description: \"支持最高 8K 分辨率，多种格式选择，保留最佳画质与音质。\",\r\n  },\r\n  {\r\n    icon: <Clapperboard className=\"h-8 w-8\" />,\r\n    title: \"强大批量处理\",\r\n    description: \"一键处理播放列表或整个频道，自动化完成批量内容获取。\",\r\n  },\r\n  {\r\n    icon: <Languages className=\"h-8 w-8\" />,\r\n    title: \"独家多语言字幕\",\r\n    description: \"下载官方、自动生成及双语字幕，满足不同语言学习和观看需求。\",\r\n  },\r\n  {\r\n    icon: <Scissors className=\"h-8 w-8\" />,\r\n    title: \"在线剪辑与创作\",\r\n    description: \"无需下载，在线完成视频剪辑、GIF 制作和铃声创作。\",\r\n  },\r\n];\r\n\r\nexport function FeatureCards() {\r\n  return (\r\n    <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4\">\r\n      {features.map((feature) => (\r\n        <Card key={feature.title}>\r\n          <CardHeader>\r\n            <div className=\"mb-4 flex justify-center text-primary\">\r\n              {feature.icon}\r\n            </div>\r\n            <CardTitle className=\"text-center\">{feature.title}</CardTitle>\r\n            <CardDescription className=\"text-center\">\r\n              {feature.description}\r\n            </CardDescription>\r\n          </CardHeader>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,WAAW;IACf;QACE,oBAAM,8OAAC,sNAAQ;YAAC,WAAU;;;;;;QAC1B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,kOAAY;YAAC,WAAU;;;;;;QAC9B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,yNAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,sNAAQ;YAAC,WAAU;;;;;;QAC1B,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,iIAAI;0BACH,cAAA,8OAAC,uIAAU;;sCACT,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,IAAI;;;;;;sCAEf,8OAAC,sIAAS;4BAAC,WAAU;sCAAe,QAAQ,KAAK;;;;;;sCACjD,8OAAC,4IAAe;4BAAC,WAAU;sCACxB,QAAQ,WAAW;;;;;;;;;;;;eAPf,QAAQ,KAAK;;;;;;;;;;AAchC", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/how-it-works.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Co<PERSON>, <PERSON>ting<PERSON>, ArrowDownToLine } from \"lucide-react\";\r\n\r\nconst steps = [\r\n  {\r\n    icon: <Copy className=\"h-10 w-10\" />,\r\n    title: \"1. 粘贴链接\",\r\n    description: \"将任何 YouTube 视频、播放列表或频道链接粘贴到输入框中。\",\r\n  },\r\n  {\r\n    icon: <Settings className=\"h-10 w-10\" />,\r\n    title: \"2. 配置选项\",\r\n    description: \"选择你需要的格式、分辨率、字幕语言或其他高级处理选项。\",\r\n  },\r\n  {\r\n    icon: <ArrowDownToLine className=\"h-10 w-10\" />,\r\n    title: \"3. 开始下载\",\r\n    description: \"点击下载按钮，获取你的内容。批量任务将在后台自动完成。\",\r\n  },\r\n];\r\n\r\nexport function HowItWorks() {\r\n  return (\r\n    <div className=\"mx-auto grid max-w-5xl grid-cols-1 items-center gap-8 md:grid-cols-3 md:gap-16\">\r\n      {steps.map((step, index) => (\r\n        <div key={step.title} className=\"relative flex flex-col items-center\">\r\n          <Card className=\"w-full\">\r\n            <CardContent className=\"flex flex-col items-center gap-4 p-8\">\r\n              <div className=\"mb-4 rounded-full bg-primary/10 p-4 text-primary\">\r\n                {step.icon}\r\n              </div>\r\n              <h3 className=\"text-xl font-bold\">{step.title}</h3>\r\n              <p className=\"text-center text-muted-foreground\">\r\n                {step.description}\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          {index < steps.length - 1 && (\r\n            <div className=\"absolute right-0 top-1/2 hidden translate-x-1/2 transform md:block\">\r\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"text-muted-foreground\">\r\n                <path d=\"M5 12h14\" />\r\n                <path d=\"m12 5 7 7-7 7\" />\r\n              </svg>\r\n            </div>\r\n          )}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,oBAAM,8OAAC,0MAAI;YAAC,WAAU;;;;;;QACtB,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,sNAAQ;YAAC,WAAU;;;;;;QAC1B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,uPAAe;YAAC,WAAU;;;;;;QACjC,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAAqB,WAAU;;kCAC9B,8OAAC,iIAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,wIAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAG,WAAU;8CAAqB,KAAK,KAAK;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;;;;;;oBAItB,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,MAAK;4BAAO,QAAO;4BAAe,aAAY;4BAAI,eAAc;4BAAQ,gBAAe;4BAAQ,WAAU;;8CACvJ,8OAAC;oCAAK,GAAE;;;;;;8CACR,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;eAhBN,KAAK,KAAK;;;;;;;;;;AAwB5B", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/faq-section.tsx"], "sourcesContent": ["import {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\n\r\nconst faqs = [\r\n  {\r\n    question: \"如何下载 YouTube 播放列表中的所有视频？\",\r\n    answer:\r\n      \"只需将播放列表的链接粘贴到主页的输入框中，点击搜索。系统会自动识别并引导你进入批量下载页面，你可以在那里选择要下载的视频和全局配置。\",\r\n  },\r\n  {\r\n    question: \"下载视频是免费的吗？\",\r\n    answer:\r\n      \"是的，我们提供强大的免费服务。免费版支持下载最高 1080p 的视频、192kbps 的音频，以及批量处理最多 20 个视频。对于需要更高质量或更大规模下载的用户，我们提供专业的 Pro 版本。\",\r\n  },\r\n  {\r\n    question: \"我可以下载受年龄限制或私有的视频吗？\",\r\n    answer:\r\n      \"不可以。本平台严格遵守 YouTube 的服务条款，无法访问或下载任何私有视频或需要登录才能观看的受限内容。\",\r\n  },\r\n  {\r\n    question: \"下载的文件安全吗？\",\r\n    answer:\r\n      \"绝对安全。所有下载处理都在我们的服务器上完成，你通过安全的签名链接获取文件。我们不会存储你的文件，临时文件也会在固定时间后被自动删除。\",\r\n  },\r\n];\r\n\r\nexport function FaqSection() {\r\n  return (\r\n    <div className=\"mx-auto max-w-3xl\">\r\n      <Accordion type=\"single\" collapsible className=\"w-full\">\r\n        {faqs.map((faq, index) => (\r\n          <AccordionItem key={index} value={`item-${index}`}>\r\n            <AccordionTrigger>{faq.question}</AccordionTrigger>\r\n            <AccordionContent>{faq.answer}</AccordionContent>\r\n          </AccordionItem>\r\n        ))}\r\n      </Accordion>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;;;AAOA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,2IAAS;YAAC,MAAK;YAAS,WAAW;YAAC,WAAU;sBAC5C,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,+IAAa;oBAAa,OAAO,CAAC,KAAK,EAAE,OAAO;;sCAC/C,8OAAC,kJAAgB;sCAAE,IAAI,QAAQ;;;;;;sCAC/B,8OAAC,kJAAgB;sCAAE,IAAI,MAAM;;;;;;;mBAFX;;;;;;;;;;;;;;;AAQ9B", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\nimport { Footer } from \"@/components/layout/footer\";\nimport { LinkInputForm } from \"@/components/features/link-input-form\";\nimport { FeatureCards } from \"@/components/features/feature-cards\";\nimport { HowItWorks } from \"@/components/features/how-it-works\";\nimport { FaqSection } from \"@/components/features/faq-section\";\n\nexport default function Home() {\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <Header />\n\n      <main className=\"flex-grow\">\n        {/* 核心交互区 */}\n        <section\n          id=\"hero\"\n          className=\"container mx-auto flex flex-col items-center justify-center px-4 py-20 text-center\"\n        >\n          <h1 className=\"text-4xl font-bold tracking-tight md:text-6xl\">\n            专业 YouTube 内容下载平台\n          </h1>\n          <p className=\"mt-4 max-w-2xl text-lg text-muted-foreground\">\n            免费下载高清视频、音频、字幕、评论等。支持单次与批量处理，提供在线剪辑与格式转换。\n          </p>\n          {/* 链接输入框 */}\n          <div className=\"mt-8 w-full max-w-2xl\">\n            <LinkInputForm />\n          </div>\n        </section>\n\n        {/* 功能特性区 */}\n        <section id=\"features\" className=\"container mx-auto px-4 py-16\">\n          <h2 className=\"mb-12 text-center text-3xl font-bold\">核心卖点</h2>\n          <FeatureCards />\n        </section>\n\n        {/* 操作指南区 */}\n        <section\n          id=\"how-it-works\"\n          className=\"bg-gray-50 py-16 dark:bg-gray-900\"\n        >\n          <div className=\"container mx-auto px-4\">\n            <h2 className=\"mb-12 text-center text-3xl font-bold\">\n              三步轻松下载\n            </h2>\n            <HowItWorks />\n          </div>\n        </section>\n\n        {/* FAQ区 */}\n        <section id=\"faq\" className=\"container mx-auto px-4 py-16\">\n          <h2 className=\"mb-12 text-center text-3xl font-bold\">常见问题</h2>\n          <FaqSection />\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBACC,IAAG;wBACH,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iKAAa;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC,2JAAY;;;;;;;;;;;kCAIf,8OAAC;wBACC,IAAG;wBACH,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,8OAAC,2JAAU;;;;;;;;;;;;;;;;kCAKf,8OAAC;wBAAQ,IAAG;wBAAM,WAAU;;0CAC1B,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC,uJAAU;;;;;;;;;;;;;;;;;0BAIf,8OAAC,yIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}