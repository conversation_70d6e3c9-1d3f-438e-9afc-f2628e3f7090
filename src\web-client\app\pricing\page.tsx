import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, X, Star } from "lucide-react";

export const metadata = {
  title: "价格方案 - YTDownloader",
  description: "选择适合您的 YTDownloader 订阅方案",
};

const plans = [
  {
    name: "免费版",
    price: "¥0",
    period: "永久免费",
    description: "适合个人轻度使用",
    popular: false,
    features: [
      { name: "视频质量", value: "最高 1080p", included: true },
      { name: "音频质量", value: "最高 192kbps", included: true },
      { name: "批量下载", value: "最多 20 个视频", included: true },
      { name: "评论下载", value: "最多 100 条", included: true },
      { name: "字幕下载", value: "支持", included: true },
      { name: "在线剪辑", value: "基础功能", included: true },
      { name: "并发任务", value: "1 个", included: true },
      { name: "文件保留", value: "24 小时", included: true },
      { name: "4K/8K 下载", value: "不支持", included: false },
      { name: "高级音频格式", value: "不支持", included: false },
      { name: "优先处理", value: "不支持", included: false },
      { name: "技术支持", value: "社区支持", included: true },
    ],
    buttonText: "开始使用",
    buttonVariant: "outline" as const,
  },
  {
    name: "专业版",
    price: "¥29",
    period: "每月",
    description: "适合专业用户和内容创作者",
    popular: true,
    features: [
      { name: "视频质量", value: "无限制（最高 8K）", included: true },
      { name: "音频质量", value: "最高 320kbps + 原始格式", included: true },
      { name: "批量下载", value: "最多 1000 个视频", included: true },
      { name: "评论下载", value: "最多 1000 条", included: true },
      { name: "字幕下载", value: "支持 + 多语言制作", included: true },
      { name: "在线剪辑", value: "高级功能", included: true },
      { name: "并发任务", value: "5 个", included: true },
      { name: "文件保留", value: "7 天", included: true },
      { name: "4K/8K 下载", value: "支持", included: true },
      { name: "高级音频格式", value: "M4A/FLAC", included: true },
      { name: "优先处理", value: "支持", included: true },
      { name: "技术支持", value: "优先邮件支持", included: true },
    ],
    buttonText: "立即升级",
    buttonVariant: "default" as const,
  },
];

const faqs = [
  {
    question: "可以随时取消订阅吗？",
    answer: "是的，您可以随时取消专业版订阅。取消后，您的账户将在当前计费周期结束时自动降级为免费版。",
  },
  {
    question: "支持哪些支付方式？",
    answer: "我们支持支付宝、微信支付、银行卡等多种支付方式。所有支付都通过安全的第三方支付平台处理。",
  },
  {
    question: "专业版有试用期吗？",
    answer: "新用户可以享受7天免费试用专业版功能。试用期结束前，您可以选择订阅或取消，不会自动扣费。",
  },
  {
    question: "下载的文件有版权问题吗？",
    answer: "用户需自行承担版权责任。我们建议仅下载您有权使用的内容，或用于个人学习研究等合理使用范围内。",
  },
];

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-4xl font-bold tracking-tight md:text-6xl">
            选择适合您的方案
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
            从免费版开始体验，随时升级到专业版享受更多高级功能
          </p>
        </section>

        {/* Pricing Cards */}
        <section className="container mx-auto px-4 pb-16">
          <div className="grid gap-8 md:grid-cols-2 max-w-4xl mx-auto">
            {plans.map((plan) => (
              <Card key={plan.name} className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''}`}>
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary">
                    <Star className="mr-1 h-3 w-3" />
                    最受欢迎
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    <span className="text-muted-foreground">/{plan.period}</span>
                  </div>
                  <p className="text-muted-foreground">{plan.description}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Button 
                    className="w-full" 
                    variant={plan.buttonVariant}
                    size="lg"
                  >
                    {plan.buttonText}
                  </Button>
                  
                  <div className="space-y-3">
                    {plan.features.map((feature) => (
                      <div key={feature.name} className="flex items-start gap-3">
                        {feature.included ? (
                          <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        ) : (
                          <X className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                        )}
                        <div className="flex-1">
                          <span className="font-medium">{feature.name}：</span>
                          <span className={feature.included ? "text-foreground" : "text-muted-foreground"}>
                            {feature.value}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* FAQ Section */}
        <section className="bg-muted/50 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-12">常见问题</h2>
              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg">{faq.question}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-16 text-center">
          <h2 className="text-3xl font-bold mb-4">准备开始了吗？</h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            立即注册免费账户，体验专业的YouTube内容下载服务
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg">免费注册</Button>
            <Button variant="outline" size="lg">联系销售</Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
