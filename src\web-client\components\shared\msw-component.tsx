'use client';

import { useEffect } from 'react';

export function MSWComponent() {
  useEffect(() => {
    async function initMSW() {
      if (process.env.NODE_ENV === 'development') {
        const { worker } = await import('@/mocks/browser');
        await worker.start({
          onUnhandledRequest: 'bypass',
        });
        console.log('🔶 MSW worker started for browser (client-side)');
      }
    }
    initMSW();
  }, []);

  return null;
}