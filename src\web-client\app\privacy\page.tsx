import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata = {
  title: "隐私政策 - YTDownloader",
  description: "YTDownloader 隐私政策和数据保护说明",
};

export default function PrivacyPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      
      <main className="flex-grow bg-muted/20">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="mb-8 text-center">
            <h1 className="text-4xl font-bold">隐私政策</h1>
            <p className="mt-4 text-lg text-muted-foreground">
              最后更新时间：{new Date().toLocaleDateString('zh-CN')}
            </p>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>1. 信息收集</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>1.1 自动收集的信息：</strong>
                  我们可能会自动收集您的IP地址、浏览器类型、操作系统、
                  访问时间等技术信息，用于改善服务质量和安全防护。
                </p>
                <p>
                  <strong>1.2 用户提供的信息：</strong>
                  当您注册账户时，我们会收集您提供的邮箱地址和密码（加密存储）。
                  匿名用户通过Cookie标识，不收集个人身份信息。
                </p>
                <p>
                  <strong>1.3 使用数据：</strong>
                  我们会记录您的下载历史、任务状态等使用数据，
                  用于提供个性化服务和技术支持。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>2. 信息使用</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>我们收集的信息仅用于以下目的：</p>
                <ul className="list-disc list-inside space-y-2">
                  <li>提供和维护下载服务</li>
                  <li>处理用户请求和技术支持</li>
                  <li>改善服务质量和用户体验</li>
                  <li>防范安全威胁和滥用行为</li>
                  <li>遵守法律法规要求</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>3. 信息共享</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>3.1 不会出售信息：</strong>
                  我们绝不会向第三方出售、交易或转让您的个人信息。
                </p>
                <p>
                  <strong>3.2 法律要求：</strong>
                  在法律要求或政府部门要求的情况下，我们可能需要披露相关信息。
                </p>
                <p>
                  <strong>3.3 服务提供商：</strong>
                  我们可能与可信的第三方服务提供商共享必要信息，
                  以提供技术支持、数据分析等服务。这些合作伙伴受到严格的保密协议约束。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>4. 数据安全</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>4.1 安全措施：</strong>
                  我们采用行业标准的安全措施保护您的数据，包括HTTPS加密传输、
                  数据库加密存储、访问控制等。
                </p>
                <p>
                  <strong>4.2 文件安全：</strong>
                  下载的文件通过签名URL访问，具有时效性。
                  临时文件会在固定时间后自动安全删除。
                </p>
                <p>
                  <strong>4.3 账户安全：</strong>
                  请妥善保管您的账户信息，使用强密码，
                  发现异常活动请及时联系我们。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>5. Cookie 使用</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>5.1 必要Cookie：</strong>
                  我们使用HttpOnly、Secure、SameSite=Lax的Cookie来维护用户会话，
                  匿名用户Cookie有效期为一年。
                </p>
                <p>
                  <strong>5.2 功能Cookie：</strong>
                  用于记住用户偏好设置，改善使用体验。
                </p>
                <p>
                  <strong>5.3 Cookie管理：</strong>
                  您可以通过浏览器设置管理Cookie，但禁用可能影响部分功能使用。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>6. 数据保留</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>6.1 账户数据：</strong>
                  注册用户的账户数据在账户有效期内保留。
                  账户删除后，相关数据将在30天内完全清除。
                </p>
                <p>
                  <strong>6.2 匿名数据：</strong>
                  长期不活跃的匿名用户数据将在180天后自动清理。
                </p>
                <p>
                  <strong>6.3 下载文件：</strong>
                  临时下载文件根据服务等级保留不同时间，
                  过期后自动删除。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>7. 用户权利</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>根据适用的数据保护法律，您享有以下权利：</p>
                <ul className="list-disc list-inside space-y-2">
                  <li>访问权：查看我们收集的关于您的信息</li>
                  <li>更正权：要求更正不准确的个人信息</li>
                  <li>删除权：要求删除您的个人信息</li>
                  <li>限制处理权：在特定情况下限制信息处理</li>
                  <li>数据可携权：以结构化格式获取您的数据</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>8. 儿童隐私</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  我们的服务不面向13岁以下儿童。
                  如果我们发现收集了13岁以下儿童的个人信息，
                  将立即删除相关信息。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>9. 政策变更</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  我们可能会不时更新本隐私政策。
                  重大变更将通过网站通知或邮件方式告知用户。
                  建议您定期查看本政策以了解最新信息。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>10. 联系我们</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  如果您对本隐私政策有任何疑问或需要行使您的权利，
                  请通过以下方式联系我们：
                </p>
                <ul className="list-disc list-inside space-y-1">
                  <li>邮箱：<EMAIL></li>
                  <li>在线客服：通过网站右下角客服窗口</li>
                  <li>邮寄地址：[公司地址]</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
