<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4"/>
        <PackageReference Include="Scalar.AspNetCore" Version="2.6.9"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Data\Migrations\"/>
        <Folder Include="Data\Models\"/>
        <Folder Include="Extensions\"/>
        <Folder Include="Filters\"/>
        <Folder Include="Middleware\"/>
        <Folder Include="Scheduled\"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

</Project>
