'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Search, X, Plus, Upload, Loader2 } from 'lucide-react';
import { analyzeUrl, ExtractedEntity, LinkType } from '@/lib/validators';
import { cn } from '@/lib/utils';

const INVALID_ENTITY: ExtractedEntity = { type: 'INVALID' };

export function LinkInputForm() {
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const singleLinkAnalysis = useMemo(() => {
    if (isBatchMode || !inputValue) return INVALID_ENTITY;
    return analyzeUrl(inputValue);
  }, [inputValue, isBatchMode]);

  const batchLinksAnalysis = useMemo(() => {
    if (!isBatchMode || !inputValue) return { validLinks: [], invalidCount: 0 };
    const lines = inputValue.split('\n').filter(line => line.trim() !== '');
    const validLinks: string[] = [];
    let invalidCount = 0;

    lines.forEach(line => {
      const res = analyzeUrl(line);
      // 批量模式只接受视频或 Shorts 链接
      if (res.type === 'VIDEO' || res.type === 'SHORTS') {
        validLinks.push(line);
      } else {
        invalidCount++;
      }
    });

    return {
      validLinks: validLinks.slice(0, 100), // 限制最多 100 个
      invalidCount,
      isTruncated: validLinks.length > 100,
    };
  }, [inputValue, isBatchMode]);


  const isValid = isBatchMode
    ? batchLinksAnalysis.validLinks.length >= 2 && batchLinksAnalysis.validLinks.length <= 100
    : singleLinkAnalysis.type !== 'INVALID';

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setInputValue(e.target.value);
    // 清除之前的错误信息
    if (errorMessage) {
      setErrorMessage('');
    }
  };

  const router = useRouter();

  const handleSubmit = async () => {
    setIsProcessing(true);
    setErrorMessage('');

    if (isBatchMode) {
      // 批量处理逻辑
      const { validLinks } = batchLinksAnalysis;
      try {
        const response = await fetch('/api/batch-jobs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sourceType: 'MULTI_VIDEO_LIST',
            videoIds: validLinks.map(link => analyzeUrl(link).videoId),
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || '创建批量任务失败，请稍后重试');
        }

        const data = await response.json();
        router.push(`/batch/${data.batchJobId}`);
      } catch (error) {
        console.error('Batch job creation failed:', error);
        setErrorMessage(error instanceof Error ? error.message : '创建批量任务失败，请稍后重试');
        setIsProcessing(false);
      }
    } else {
      // 单次处理逻辑
      const { type, videoId, playlistId, channelId } = singleLinkAnalysis;
      switch (type) {
        case 'VIDEO':
        case 'SHORTS':
          if (videoId) {
            router.push(`/download/${videoId}`);
          } else {
            setErrorMessage('无法提取视频ID，请检查链接格式');
            setIsProcessing(false);
          }
          break;
        case 'PLAYLIST':
        case 'CHANNEL':
          try {
            const response = await fetch('/api/batch-jobs', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                sourceType: type,
                sourceIdentifier:
                  type === 'PLAYLIST' ? { playlistId } : { channelId },
              }),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(errorData.message || '创建批量任务失败，请稍后重试');
            }

            const data = await response.json();
            router.push(`/batch/${data.batchJobId}`);
          } catch (error) {
            console.error('Batch job creation failed:', error);
            setErrorMessage(error instanceof Error ? error.message : '创建批量任务失败，请稍后重试');
            setIsProcessing(false);
          }
          break;
        default:
          setErrorMessage('不支持的链接类型，请检查输入的链接');
          setIsProcessing(false);
          break;
      }
    }
  };

  const clearInput = () => {
    setInputValue('');
  };

  const getSingleLinkHint = () => {
    if (!inputValue) return ' ';
    switch (singleLinkAnalysis.type) {
      case 'VIDEO':
        return '[✓] 检测到单个视频链接';
      case 'SHORTS':
        return '[✓] 检测到 Shorts 链接';
      case 'PLAYLIST':
        return '[✓] 检测到播放列表链接';
      case 'CHANNEL':
        return '[✓] 检测到频道链接';
      case 'INVALID':
        return '[✗] 无效的链接或不受支持的格式';
      default:
        return ' ';
    }
  };

  const getInputBorderColor = () => {
    if (!inputValue) return 'border-border';
    return isValid ? 'border-green-500' : 'border-red-500';
  };


  if (isBatchMode) {
    const { validLinks, invalidCount, isTruncated } = batchLinksAnalysis;
    const validCount = validLinks.length;
    return (
      <div className="relative w-full">
        <Textarea
          placeholder="每行粘贴一个视频或 Shorts 链接（最多 100 个）..."
          className={cn('h-48 resize-none p-4 pr-20', getInputBorderColor())}
          value={inputValue}
          onChange={handleInputChange}
          disabled={isProcessing}
        />
        <div className="absolute right-3 top-3 flex flex-col gap-2">
          <Button size="icon" variant="ghost" onClick={clearInput} disabled={isProcessing}>
            <X className="h-4 w-4" />
          </Button>
          <Button size="icon" variant="ghost" disabled={isProcessing}>
            <Upload className="h-4 w-4" />
          </Button>
        </div>
        <div className="mt-2 text-sm text-muted-foreground">
          <p>检测到 {validCount} 个有效链接。{invalidCount > 0 && `已忽略 ${invalidCount} 个无效或非视频链接。`}</p>
          {isTruncated && <p className="text-amber-600">输入超过100个，超出部分已被忽略。</p>}
          {errorMessage && (
            <p className="text-red-600 mt-1">{errorMessage}</p>
          )}
        </div>
        <div className="mt-4 flex justify-end gap-2">
           <Button variant="ghost" onClick={() => setIsBatchMode(false)} disabled={isProcessing}>
            返回单次模式
          </Button>
          <Button disabled={!isValid || isProcessing} onClick={handleSubmit}>
            {isProcessing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Search className="mr-2 h-4 w-4" />
            )}
            处理 {validCount} 个链接
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="relative">
        <Input
          placeholder="粘贴视频、播放列表或频道链接..."
          className={cn('h-14 p-4 pr-10 text-lg', getInputBorderColor())}
          value={inputValue}
          onChange={handleInputChange}
          disabled={isProcessing}
        />
        {inputValue && !isProcessing && (
          <Button
            size="icon"
            variant="ghost"
            className="absolute right-2 top-1/2 -translate-y-1/2"
            onClick={clearInput}
          >
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>
      <div className="mt-2 flex items-center justify-between">
        <div className="flex-1">
          <p className={cn("text-sm", isValid ? 'text-green-600' : 'text-red-600')}>
            {getSingleLinkHint()}
          </p>
          {errorMessage && (
            <p className="text-sm text-red-600 mt-1">{errorMessage}</p>
          )}
        </div>
        <Button variant="link" size="sm" onClick={() => setIsBatchMode(true)} disabled={isProcessing}>
          <Plus className="mr-1 h-4 w-4" />
          批量添加
        </Button>
      </div>
      <Button size="lg" className="mt-4 w-full" disabled={!isValid || isProcessing} onClick={handleSubmit}>
         {isProcessing ? (
          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
        ) : (
          <Search className="mr-2 h-5 w-5" />
        )}
        搜索
      </Button>
    </div>
  );
}