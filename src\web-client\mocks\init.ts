/**
 * MSW 服务器端初始化
 * 仅在开发环境的服务器端启动MSW
 */

// 全局标记，防止重复初始化
declare global {
  var __MSW_SERVER_INITIALIZED__: boolean | undefined;
}

// 只在开发环境和服务器端初始化MSW
if (process.env.NODE_ENV === 'development' && typeof window === 'undefined') {
  if (!global.__MSW_SERVER_INITIALIZED__) {
    global.__MSW_SERVER_INITIALIZED__ = true;

    try {
      const { setupServer } = require('msw/node');
      const { handlers } = require('./handlers');

      const server = setupServer(...handlers);
      server.listen({
        onUnhandledRequest: 'bypass',
      });

      console.log('🔶 MSW server started for Node.js (server-side)');

    } catch (error) {
      console.error('❌ Failed to initialize MSW server:', error);
    }
  }
}

export {};
