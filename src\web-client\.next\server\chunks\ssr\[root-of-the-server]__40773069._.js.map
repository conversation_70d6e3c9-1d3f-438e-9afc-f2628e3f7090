{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/validators.ts"], "sourcesContent": ["/**\r\n * YouTube 链接验证相关的工具函数\r\n */\r\n\r\n// 正则表达式，用于匹配 YouTube 视频、Shorts、播放列表和频道链接\r\nconst YOUTUBE_URL_REGEX =\r\n  /^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtu\\.be)\\/(watch\\?v=|embed\\/|v\\/|shorts\\/|playlist\\?list=|channel\\/|user\\/|c\\/)?([a-zA-Z0-9\\-_]+)/;\r\n\r\n/**\r\n * 链接类型\r\n * - VIDEO: 单个视频\r\n * - SHORTS: 短视频\r\n * - PLAYLIST: 播放列表\r\n * - CHANNEL: 频道\r\n * - MULTI_VIDEO_LIST: 多个视频链接\r\n * - INVALID: 无效链接\r\n */\r\nexport type LinkType =\r\n  | 'VIDEO'\r\n  | 'SHORTS'\r\n  | 'PLAYLIST'\r\n  | 'CHANNEL'\r\n  | 'MULTI_VIDEO_LIST'\r\n  | 'INVALID';\r\n\r\n/**\r\n * 从链接中提取的实体信息\r\n */\r\nexport interface ExtractedEntity {\r\n  type: LinkType;\r\n  videoId?: string | null;\r\n  playlistId?: string | null;\r\n  channelId?: string | null;\r\n}\r\n\r\n/**\r\n * 分析单个 URL，判断其类型并提取相关 ID\r\n * @param url 要分析的 URL\r\n * @returns 返回一个包含链接类型和提取出的 ID 的对象\r\n */\r\nexport function analyzeUrl(url: string): ExtractedEntity {\r\n  if (!url || !YOUTUBE_URL_REGEX.test(url.trim())) {\r\n    return { type: 'INVALID' };\r\n  }\r\n\r\n  const trimmedUrl = url.trim();\r\n\r\n  try {\r\n    const urlObj = new URL(trimmedUrl);\r\n    const urlParams = new URLSearchParams(urlObj.search);\r\n\r\n    // 首先检查 Shorts 链接 (youtube.com/shorts/VIDEO_ID)\r\n    const shortsMatch = trimmedUrl.match(/\\/shorts\\/([a-zA-Z0-9\\-_]+)/);\r\n    if (shortsMatch && shortsMatch[1]) {\r\n      return { type: 'SHORTS', videoId: shortsMatch[1] };\r\n    }\r\n\r\n    // 规则: 当 v= 和 list= 共存时，优先识别为视频链接\r\n    if (urlParams.has('v')) {\r\n      const videoId = urlParams.get('v');\r\n      if (videoId) {\r\n        return { type: 'VIDEO', videoId };\r\n      }\r\n    }\r\n\r\n    // 检查播放列表链接\r\n    if (urlParams.has('list')) {\r\n      const playlistId = urlParams.get('list');\r\n      if (playlistId) {\r\n        return { type: 'PLAYLIST', playlistId };\r\n      }\r\n    }\r\n\r\n    // 检查频道链接\r\n    const channelMatch = trimmedUrl.match(/\\/channel\\/([a-zA-Z0-9\\-_]+)/);\r\n    if (channelMatch && channelMatch[1]) {\r\n      return { type: 'CHANNEL', channelId: channelMatch[1] };\r\n    }\r\n\r\n    // 检查用户链接 (转换为频道)\r\n    const userMatch = trimmedUrl.match(/\\/(user|c)\\/([a-zA-Z0-9\\-_]+)/);\r\n    if (userMatch && userMatch[2]) {\r\n      return { type: 'CHANNEL', channelId: userMatch[2] };\r\n    }\r\n\r\n    // 检查 youtu.be/xxxx 格式\r\n    const shortLinkMatch = trimmedUrl.match(/youtu\\.be\\/([a-zA-Z0-9\\-_]+)/);\r\n    if (shortLinkMatch && shortLinkMatch[1]) {\r\n      return { type: 'VIDEO', videoId: shortLinkMatch[1] };\r\n    }\r\n\r\n    return { type: 'INVALID' };\r\n  } catch (error) {\r\n    // URL 构造失败，返回无效\r\n    return { type: 'INVALID' };\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;CAEC,GAED,yCAAyC;;;;;AACzC,MAAM,oBACJ;AAkCK,SAAS,WAAW,GAAW;IACpC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,IAAI,IAAI,KAAK;QAC/C,OAAO;YAAE,MAAM;QAAU;IAC3B;IAEA,MAAM,aAAa,IAAI,IAAI;IAE3B,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,IAAI,gBAAgB,OAAO,MAAM;QAEnD,+CAA+C;QAC/C,MAAM,cAAc,WAAW,KAAK,CAAC;QACrC,IAAI,eAAe,WAAW,CAAC,EAAE,EAAE;YACjC,OAAO;gBAAE,MAAM;gBAAU,SAAS,WAAW,CAAC,EAAE;YAAC;QACnD;QAEA,iCAAiC;QACjC,IAAI,UAAU,GAAG,CAAC,MAAM;YACtB,MAAM,UAAU,UAAU,GAAG,CAAC;YAC9B,IAAI,SAAS;gBACX,OAAO;oBAAE,MAAM;oBAAS;gBAAQ;YAClC;QACF;QAEA,WAAW;QACX,IAAI,UAAU,GAAG,CAAC,SAAS;YACzB,MAAM,aAAa,UAAU,GAAG,CAAC;YACjC,IAAI,YAAY;gBACd,OAAO;oBAAE,MAAM;oBAAY;gBAAW;YACxC;QACF;QAEA,SAAS;QACT,MAAM,eAAe,WAAW,KAAK,CAAC;QACtC,IAAI,gBAAgB,YAAY,CAAC,EAAE,EAAE;YACnC,OAAO;gBAAE,MAAM;gBAAW,WAAW,YAAY,CAAC,EAAE;YAAC;QACvD;QAEA,iBAAiB;QACjB,MAAM,YAAY,WAAW,KAAK,CAAC;QACnC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;YAC7B,OAAO;gBAAE,MAAM;gBAAW,WAAW,SAAS,CAAC,EAAE;YAAC;QACpD;QAEA,sBAAsB;QACtB,MAAM,iBAAiB,WAAW,KAAK,CAAC;QACxC,IAAI,kBAAkB,cAAc,CAAC,EAAE,EAAE;YACvC,OAAO;gBAAE,MAAM;gBAAS,SAAS,cAAc,CAAC,EAAE;YAAC;QACrD;QAEA,OAAO;YAAE,MAAM;QAAU;IAC3B,EAAE,OAAO,OAAO;QACd,gBAAgB;QAChB,OAAO;YAAE,MAAM;QAAU;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/features/link-input-form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useMemo } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Search, X, Plus, Upload, Loader2 } from 'lucide-react';\r\nimport { analyzeUrl, ExtractedEntity, LinkType } from '@/lib/validators';\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst INVALID_ENTITY: ExtractedEntity = { type: 'INVALID' };\r\n\r\nexport function LinkInputForm() {\r\n  const [isBatchMode, setIsBatchMode] = useState(false);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n\r\n  const singleLinkAnalysis = useMemo(() => {\r\n    if (isBatchMode || !inputValue) return INVALID_ENTITY;\r\n    return analyzeUrl(inputValue);\r\n  }, [inputValue, isBatchMode]);\r\n\r\n  const batchLinksAnalysis = useMemo(() => {\r\n    if (!isBatchMode || !inputValue) return { validLinks: [], invalidCount: 0 };\r\n    const lines = inputValue.split('\\n').filter(line => line.trim() !== '');\r\n    const validLinks: string[] = [];\r\n    let invalidCount = 0;\r\n\r\n    lines.forEach(line => {\r\n      const res = analyzeUrl(line);\r\n      // 批量模式只接受视频或 Shorts 链接\r\n      if (res.type === 'VIDEO' || res.type === 'SHORTS') {\r\n        validLinks.push(line);\r\n      } else {\r\n        invalidCount++;\r\n      }\r\n    });\r\n\r\n    return {\r\n      validLinks: validLinks.slice(0, 100), // 限制最多 100 个\r\n      invalidCount,\r\n      isTruncated: validLinks.length > 100,\r\n    };\r\n  }, [inputValue, isBatchMode]);\r\n\r\n\r\n  const isValid = isBatchMode\r\n    ? batchLinksAnalysis.validLinks.length >= 2 && batchLinksAnalysis.validLinks.length <= 100\r\n    : singleLinkAnalysis.type !== 'INVALID';\r\n\r\n  const handleInputChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\r\n  ) => {\r\n    setInputValue(e.target.value);\r\n    // 清除之前的错误信息\r\n    if (errorMessage) {\r\n      setErrorMessage('');\r\n    }\r\n  };\r\n\r\n  const router = useRouter();\r\n\r\n  const handleSubmit = async () => {\r\n    setIsProcessing(true);\r\n    setErrorMessage('');\r\n\r\n    if (isBatchMode) {\r\n      // 批量处理逻辑\r\n      const { validLinks } = batchLinksAnalysis;\r\n      try {\r\n        const response = await fetch('/api/batch-jobs', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({\r\n            sourceType: 'MULTI_VIDEO_LIST',\r\n            videoIds: validLinks.map(link => analyzeUrl(link).videoId),\r\n          }),\r\n        });\r\n\r\n        if (!response.ok) {\r\n          const errorData = await response.json().catch(() => ({}));\r\n          throw new Error(errorData.message || '创建批量任务失败，请稍后重试');\r\n        }\r\n\r\n        const data = await response.json();\r\n        router.push(`/batch/${data.batchJobId}`);\r\n      } catch (error) {\r\n        console.error('Batch job creation failed:', error);\r\n        setErrorMessage(error instanceof Error ? error.message : '创建批量任务失败，请稍后重试');\r\n        setIsProcessing(false);\r\n      }\r\n    } else {\r\n      // 单次处理逻辑\r\n      const { type, videoId, playlistId, channelId } = singleLinkAnalysis;\r\n      switch (type) {\r\n        case 'VIDEO':\r\n        case 'SHORTS':\r\n          if (videoId) {\r\n            router.push(`/download/${videoId}`);\r\n          } else {\r\n            setErrorMessage('无法提取视频ID，请检查链接格式');\r\n            setIsProcessing(false);\r\n          }\r\n          break;\r\n        case 'PLAYLIST':\r\n        case 'CHANNEL':\r\n          try {\r\n            const response = await fetch('/api/batch-jobs', {\r\n              method: 'POST',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                sourceType: type,\r\n                sourceIdentifier:\r\n                  type === 'PLAYLIST' ? { playlistId } : { channelId },\r\n              }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n              const errorData = await response.json().catch(() => ({}));\r\n              throw new Error(errorData.message || '创建批量任务失败，请稍后重试');\r\n            }\r\n\r\n            const data = await response.json();\r\n            router.push(`/batch/${data.batchJobId}`);\r\n          } catch (error) {\r\n            console.error('Batch job creation failed:', error);\r\n            setErrorMessage(error instanceof Error ? error.message : '创建批量任务失败，请稍后重试');\r\n            setIsProcessing(false);\r\n          }\r\n          break;\r\n        default:\r\n          setErrorMessage('不支持的链接类型，请检查输入的链接');\r\n          setIsProcessing(false);\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const clearInput = () => {\r\n    setInputValue('');\r\n  };\r\n\r\n  const getSingleLinkHint = () => {\r\n    if (!inputValue) return ' ';\r\n    switch (singleLinkAnalysis.type) {\r\n      case 'VIDEO':\r\n        return '[✓] 检测到单个视频链接';\r\n      case 'SHORTS':\r\n        return '[✓] 检测到 Shorts 链接';\r\n      case 'PLAYLIST':\r\n        return '[✓] 检测到播放列表链接';\r\n      case 'CHANNEL':\r\n        return '[✓] 检测到频道链接';\r\n      case 'INVALID':\r\n        return '[✗] 无效的链接或不受支持的格式';\r\n      default:\r\n        return ' ';\r\n    }\r\n  };\r\n\r\n  const getInputBorderColor = () => {\r\n    if (!inputValue) return 'border-border';\r\n    return isValid ? 'border-green-500' : 'border-red-500';\r\n  };\r\n\r\n\r\n  if (isBatchMode) {\r\n    const { validLinks, invalidCount, isTruncated } = batchLinksAnalysis;\r\n    const validCount = validLinks.length;\r\n    return (\r\n      <div className=\"relative w-full\">\r\n        <Textarea\r\n          placeholder=\"每行粘贴一个视频或 Shorts 链接（最多 100 个）...\"\r\n          className={cn('h-48 resize-none p-4 pr-20', getInputBorderColor())}\r\n          value={inputValue}\r\n          onChange={handleInputChange}\r\n          disabled={isProcessing}\r\n        />\r\n        <div className=\"absolute right-3 top-3 flex flex-col gap-2\">\r\n          <Button size=\"icon\" variant=\"ghost\" onClick={clearInput} disabled={isProcessing}>\r\n            <X className=\"h-4 w-4\" />\r\n          </Button>\r\n          <Button size=\"icon\" variant=\"ghost\" disabled={isProcessing}>\r\n            <Upload className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n        <div className=\"mt-2 text-sm text-muted-foreground\">\r\n          <p>检测到 {validCount} 个有效链接。{invalidCount > 0 && `已忽略 ${invalidCount} 个无效或非视频链接。`}</p>\r\n          {isTruncated && <p className=\"text-amber-600\">输入超过100个，超出部分已被忽略。</p>}\r\n          {errorMessage && (\r\n            <p className=\"text-red-600 mt-1\">{errorMessage}</p>\r\n          )}\r\n        </div>\r\n        <div className=\"mt-4 flex justify-end gap-2\">\r\n           <Button variant=\"ghost\" onClick={() => setIsBatchMode(false)} disabled={isProcessing}>\r\n            返回单次模式\r\n          </Button>\r\n          <Button disabled={!isValid || isProcessing} onClick={handleSubmit}>\r\n            {isProcessing ? (\r\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n            ) : (\r\n              <Search className=\"mr-2 h-4 w-4\" />\r\n            )}\r\n            处理 {validCount} 个链接\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className=\"relative\">\r\n        <Input\r\n          placeholder=\"粘贴视频、播放列表或频道链接...\"\r\n          className={cn('h-14 p-4 pr-10 text-lg', getInputBorderColor())}\r\n          value={inputValue}\r\n          onChange={handleInputChange}\r\n          disabled={isProcessing}\r\n        />\r\n        {inputValue && !isProcessing && (\r\n          <Button\r\n            size=\"icon\"\r\n            variant=\"ghost\"\r\n            className=\"absolute right-2 top-1/2 -translate-y-1/2\"\r\n            onClick={clearInput}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2 flex items-center justify-between\">\r\n        <div className=\"flex-1\">\r\n          <p className={cn(\"text-sm\", isValid ? 'text-green-600' : 'text-red-600')}>\r\n            {getSingleLinkHint()}\r\n          </p>\r\n          {errorMessage && (\r\n            <p className=\"text-sm text-red-600 mt-1\">{errorMessage}</p>\r\n          )}\r\n        </div>\r\n        <Button variant=\"link\" size=\"sm\" onClick={() => setIsBatchMode(true)} disabled={isProcessing}>\r\n          <Plus className=\"mr-1 h-4 w-4\" />\r\n          批量添加\r\n        </Button>\r\n      </div>\r\n      <Button size=\"lg\" className=\"mt-4 w-full\" disabled={!isValid || isProcessing} onClick={handleSubmit}>\r\n         {isProcessing ? (\r\n          <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\r\n        ) : (\r\n          <Search className=\"mr-2 h-5 w-5\" />\r\n        )}\r\n        搜索\r\n      </Button>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,iBAAkC;IAAE,MAAM;AAAU;AAEnD,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,MAAM,qBAAqB,IAAA,gNAAO,EAAC;QACjC,IAAI,eAAe,CAAC,YAAY,OAAO;QACvC,OAAO,IAAA,+HAAU,EAAC;IACpB,GAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,qBAAqB,IAAA,gNAAO,EAAC;QACjC,IAAI,CAAC,eAAe,CAAC,YAAY,OAAO;YAAE,YAAY,EAAE;YAAE,cAAc;QAAE;QAC1E,MAAM,QAAQ,WAAW,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,OAAO;QACpE,MAAM,aAAuB,EAAE;QAC/B,IAAI,eAAe;QAEnB,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,MAAM,IAAA,+HAAU,EAAC;YACvB,uBAAuB;YACvB,IAAI,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,UAAU;gBACjD,WAAW,IAAI,CAAC;YAClB,OAAO;gBACL;YACF;QACF;QAEA,OAAO;YACL,YAAY,WAAW,KAAK,CAAC,GAAG;YAChC;YACA,aAAa,WAAW,MAAM,GAAG;QACnC;IACF,GAAG;QAAC;QAAY;KAAY;IAG5B,MAAM,UAAU,cACZ,mBAAmB,UAAU,CAAC,MAAM,IAAI,KAAK,mBAAmB,UAAU,CAAC,MAAM,IAAI,MACrF,mBAAmB,IAAI,KAAK;IAEhC,MAAM,oBAAoB,CACxB;QAEA,cAAc,EAAE,MAAM,CAAC,KAAK;QAC5B,YAAY;QACZ,IAAI,cAAc;YAChB,gBAAgB;QAClB;IACF;IAEA,MAAM,SAAS,IAAA,+IAAS;IAExB,MAAM,eAAe;QACnB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI,aAAa;YACf,SAAS;YACT,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,YAAY;wBACZ,UAAU,WAAW,GAAG,CAAC,CAAA,OAAQ,IAAA,+HAAU,EAAC,MAAM,OAAO;oBAC3D;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;oBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;gBACvC;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,UAAU,EAAE;YACzC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACzD,gBAAgB;YAClB;QACF,OAAO;YACL,SAAS;YACT,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;YACjD,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,IAAI,SAAS;wBACX,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS;oBACpC,OAAO;wBACL,gBAAgB;wBAChB,gBAAgB;oBAClB;oBACA;gBACF,KAAK;gBACL,KAAK;oBACH,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;4BAC9C,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,YAAY;gCACZ,kBACE,SAAS,aAAa;oCAAE;gCAAW,IAAI;oCAAE;gCAAU;4BACvD;wBACF;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;4BACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;wBACvC;wBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,UAAU,EAAE;oBACzC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBACzD,gBAAgB;oBAClB;oBACA;gBACF;oBACE,gBAAgB;oBAChB,gBAAgB;oBAChB;YACJ;QACF;IACF;IAEA,MAAM,aAAa;QACjB,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAQ,mBAAmB,IAAI;YAC7B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,UAAU,qBAAqB;IACxC;IAGA,IAAI,aAAa;QACf,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG;QAClD,MAAM,aAAa,WAAW,MAAM;QACpC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,yIAAQ;oBACP,aAAY;oBACZ,WAAW,IAAA,kHAAE,EAAC,8BAA8B;oBAC5C,OAAO;oBACP,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qIAAM;4BAAC,MAAK;4BAAO,SAAQ;4BAAQ,SAAS;4BAAY,UAAU;sCACjE,cAAA,8OAAC,iMAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,8OAAC,qIAAM;4BAAC,MAAK;4BAAO,SAAQ;4BAAQ,UAAU;sCAC5C,cAAA,8OAAC,gNAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAE;gCAAK;gCAAW;gCAAQ,eAAe,KAAK,CAAC,IAAI,EAAE,aAAa,WAAW,CAAC;;;;;;;wBAC9E,6BAAe,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;wBAC7C,8BACC,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAGtC,8OAAC;oBAAI,WAAU;;sCACZ,8OAAC,qIAAM;4BAAC,SAAQ;4BAAQ,SAAS,IAAM,eAAe;4BAAQ,UAAU;sCAAc;;;;;;sCAGvF,8OAAC,qIAAM;4BAAC,UAAU,CAAC,WAAW;4BAAc,SAAS;;gCAClD,6BACC,8OAAC,4NAAO;oCAAC,WAAU;;;;;yDAEnB,8OAAC,gNAAM;oCAAC,WAAU;;;;;;gCAClB;gCACE;gCAAW;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,mIAAK;wBACJ,aAAY;wBACZ,WAAW,IAAA,kHAAE,EAAC,0BAA0B;wBACxC,OAAO;wBACP,UAAU;wBACV,UAAU;;;;;;oBAEX,cAAc,CAAC,8BACd,8OAAC,qIAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,iMAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAW,IAAA,kHAAE,EAAC,WAAW,UAAU,mBAAmB;0CACtD;;;;;;4BAEF,8BACC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG9C,8OAAC,qIAAM;wBAAC,SAAQ;wBAAO,MAAK;wBAAK,SAAS,IAAM,eAAe;wBAAO,UAAU;;0CAC9E,8OAAC,0MAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAIrC,8OAAC,qIAAM;gBAAC,MAAK;gBAAK,WAAU;gBAAc,UAAU,CAAC,WAAW;gBAAc,SAAS;;oBACnF,6BACA,8OAAC,4NAAO;wBAAC,WAAU;;;;;6CAEnB,8OAAC,gNAAM;wBAAC,WAAU;;;;;;oBAClB;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,8OAAC,6KAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,6KAAuB;QACtB,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,+KAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,gLAA0B;YACzB,aAAU;YACV,WAAW,IAAA,kHAAE,EACX,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,2OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,gLAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,IAAA,kHAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}]}