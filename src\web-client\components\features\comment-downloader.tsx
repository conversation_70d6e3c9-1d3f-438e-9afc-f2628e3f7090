'use client';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CommentDownloaderProps {
  commentsDisabled: boolean;
}

export function CommentDownloader({ commentsDisabled }: CommentDownloaderProps) {
  if (commentsDisabled) {
    return <p className="text-sm text-muted-foreground">此视频的评论功能已关闭。</p>;
  }

  // 这里的交互和升级提示等将在后续任务中完善
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <Select defaultValue="hot">
          <SelectTrigger>
            <SelectValue placeholder="排序方式" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="hot">热门评论</SelectItem>
            <SelectItem value="new">最新评论</SelectItem>
          </SelectContent>
        </Select>
        <Select defaultValue="100">
          <SelectTrigger>
            <SelectValue placeholder="下载数量" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="100">前 100 条</SelectItem>
            <SelectItem value="500" disabled>前 500 条 (Pro)</SelectItem>
            <SelectItem value="1000" disabled>前 1000 条 (Pro)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" className="flex-1">
          下载 XLSX
        </Button>
        <Button variant="outline" size="sm" className="flex-1">
          下载 CSV
        </Button>
      </div>
    </div>
  );
}