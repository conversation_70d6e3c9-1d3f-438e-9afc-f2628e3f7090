/**
 * YouTube 链接验证相关的工具函数
 */

// 正则表达式，用于匹配 YouTube 视频、Shorts、播放列表和频道链接
const YOUTUBE_URL_REGEX =
  /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/(watch\?v=|embed\/|v\/|shorts\/|playlist\?list=|channel\/|user\/|c\/)?([a-zA-Z0-9\-_]+)/;

/**
 * 链接类型
 * - VIDEO: 单个视频
 * - SHORTS: 短视频
 * - PLAYLIST: 播放列表
 * - CHANNEL: 频道
 * - MULTI_VIDEO_LIST: 多个视频链接
 * - INVALID: 无效链接
 */
export type LinkType =
  | 'VIDEO'
  | 'SHORTS'
  | 'PLAYLIST'
  | 'CHANNEL'
  | 'MULTI_VIDEO_LIST'
  | 'INVALID';

/**
 * 从链接中提取的实体信息
 */
export interface ExtractedEntity {
  type: LinkType;
  videoId?: string | null;
  playlistId?: string | null;
  channelId?: string | null;
}

/**
 * 分析单个 URL，判断其类型并提取相关 ID
 * @param url 要分析的 URL
 * @returns 返回一个包含链接类型和提取出的 ID 的对象
 */
export function analyzeUrl(url: string): ExtractedEntity {
  if (!url || !YOUTUBE_URL_REGEX.test(url.trim())) {
    return { type: 'INVALID' };
  }

  const trimmedUrl = url.trim();
  const urlParams = new URLSearchParams(new URL(trimmedUrl).search);

  // 规则: 当 v= 和 list= 共存时，优先识别为视频链接
  if (urlParams.has('v')) {
    const videoId = urlParams.get('v');
    if (videoId) {
       // 检查是否为 Shorts 链接
       if (trimmedUrl.includes('/shorts/')) {
        return { type: 'SHORTS', videoId };
      }
      return { type: 'VIDEO', videoId };
    }
  }

  if (urlParams.has('list')) {
    const playlistId = urlParams.get('list');
    if (playlistId) {
      return { type: 'PLAYLIST', playlistId };
    }
  }

  const match = trimmedUrl.match(/\/channel\/([a-zA-Z0-9\-_]+)/);
  if (match && match[1]) {
    return { type: 'CHANNEL', channelId: match[1] };
  }
  
  // 补充 youtu.be/xxxx 格式
  const shortLinkMatch = trimmedUrl.match(/youtu\.be\/([a-zA-Z0-9\-_]+)/);
  if(shortLinkMatch && shortLinkMatch[1]){
    return { type: 'VIDEO', videoId: shortLinkMatch[1] };
  }

  return { type: 'INVALID' };
}