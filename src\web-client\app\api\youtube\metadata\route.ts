import { NextRequest, NextResponse } from 'next/server';
import { mockVideoPageData } from '@/mocks/data/video-page-data';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const videoId = searchParams.get('videoId');

  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  console.log(`🔶 API Route intercepted: /api/youtube/metadata?videoId=${videoId}`);

  if (videoId === 'error') {
    return NextResponse.json(
      { error: 'Video not found' },
      { status: 404 }
    );
  }

  if (videoId === 'dQw4w9WgXcQ') {
    return NextResponse.json(mockVideoPageData);
  }
  
  // 对于任何其他 videoId，也返回成功的数据
  return NextResponse.json({
    ...mockVideoPageData,
    id: videoId,
    title: `(Mocked) Video Title for ${videoId}`,
  });
}
