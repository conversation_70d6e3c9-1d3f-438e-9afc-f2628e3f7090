import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { LinkInputForm } from "@/components/features/link-input-form";
import { FeatureCards } from "@/components/features/feature-cards";
import { HowItWorks } from "@/components/features/how-it-works";
import { FaqSection } from "@/components/features/faq-section";

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-grow">
        {/* 核心交互区 */}
        <section
          id="hero"
          className="container mx-auto flex flex-col items-center justify-center px-4 py-20 text-center"
        >
          <h1 className="text-4xl font-bold tracking-tight md:text-6xl">
            专业 YouTube 内容下载平台
          </h1>
          <p className="mt-4 max-w-2xl text-lg text-muted-foreground">
            免费下载高清视频、音频、字幕、评论等。支持单次与批量处理，提供在线剪辑与格式转换。
          </p>
          {/* 链接输入框 */}
          <div className="mt-8 w-full max-w-2xl">
            <LinkInputForm />
          </div>
        </section>

        {/* 功能特性区 */}
        <section id="features" className="container mx-auto px-4 py-16">
          <h2 className="mb-12 text-center text-3xl font-bold">核心卖点</h2>
          <FeatureCards />
        </section>

        {/* 操作指南区 */}
        <section
          id="how-it-works"
          className="bg-gray-50 py-16 dark:bg-gray-900"
        >
          <div className="container mx-auto px-4">
            <h2 className="mb-12 text-center text-3xl font-bold">
              三步轻松下载
            </h2>
            <HowItWorks />
          </div>
        </section>

        {/* FAQ区 */}
        <section id="faq" className="container mx-auto px-4 py-16">
          <h2 className="mb-12 text-center text-3xl font-bold">常见问题</h2>
          <FaqSection />
        </section>
      </main>

      <Footer />
    </div>
  );
}
