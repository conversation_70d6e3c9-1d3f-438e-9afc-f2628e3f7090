{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/data/video-page-data.ts"], "sourcesContent": ["import { VideoPageData } from '@/lib/types/definitions';\r\n\r\n/**\r\n * 单次下载页的模拟数据\r\n * videoId: dQw4w9WgXcQ (<PERSON> - Never Gonna Give You Up)\r\n */\r\nexport const mockVideoPageData: VideoPageData = {\r\n  id: 'dQw4w9WgXcQ',\r\n  title: '<PERSON> - Never Gonna Give You Up (Official Music Video)',\r\n  description:\r\n    'The official video for “Never Gonna Give You Up” by <PERSON>...\\n' +\r\n    'Listen to <PERSON>: https://RickAs<PERSON>.lnk.to/listenYD\\n' +\r\n    'Subscribe to the official <PERSON> YouTube channel: https://RickAstley.lnk.to/subscribeYD\\n\\n' +\r\n    'Follow <PERSON>:\\n' +\r\n    'Facebook: https://RickAstley.lnk.to/followFI\\n' +\r\n    'Twitter: https://RickAstley.lnk.to/followTI\\n' +\r\n    'Instagram: https://RickAstley.lnk.to/followII\\n' +\r\n    'Website: https://RickAstley.lnk.to/followWI\\n' +\r\n    'Spotify: https://RickAstley.lnk.to/followSI\\n' +\r\n    'YouTube: https://RickAstley.lnk.to/subscribeYD',\r\n  duration: 212,\r\n  channelName: 'RickAstleyVEVO',\r\n  channelUrl: 'https://www.youtube.com/user/<PERSON>tleyVEVO',\r\n  uploadDate: '2009-10-25T06:57:33Z',\r\n  viewCount: 1589732984,\r\n  commentCount: 2345678,\r\n  commentsDisabled: false,\r\n  thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',\r\n  thumbnails: [\r\n    { format: 'jpg', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg' },\r\n    { format: 'png', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png' }, // 示例, png 通常不存在\r\n  ],\r\n  videoStreams: [\r\n    { qualityLabel: '1080p HD', resolution: '1920x1080', fps: 60, fileSize: 98765432, format: 'MP4', downloadId: 'v1' },\r\n    { qualityLabel: '720p', resolution: '1280x720', fps: 30, fileSize: 54321098, format: 'MP4', downloadId: 'v2' },\r\n    { qualityLabel: '480p', resolution: '854x480', fps: 30, fileSize: 23456789, format: 'WebM', downloadId: 'v3' },\r\n  ],\r\n  audioStreams: [\r\n    { qualityLabel: 'High', resolution: '', fps: 0, fileSize: 5678901, format: 'M4A', bitrate: 128, downloadId: 'a1' },\r\n    { qualityLabel: 'Medium', resolution: '', fps: 0, fileSize: 3456789, format: 'WebM', bitrate: 160, downloadId: 'a2' },\r\n    { qualityLabel: 'MP3 320kbps', resolution: '', fps: 0, fileSize: 7890123, format: 'MP3', bitrate: 320, downloadId: 'a3-320' },\r\n    { qualityLabel: 'MP3 192kbps', resolution: '', fps: 0, fileSize: 4567890, format: 'MP3', bitrate: 192, downloadId: 'a3-192' },\r\n  ],\r\n  subtitles: [\r\n    { langCode: 'en', langName: 'English', isAutoGenerated: false },\r\n    { langCode: 'es', langName: 'Spanish', isAutoGenerated: false },\r\n    { langCode: 'fr', langName: 'French', isAutoGenerated: true },\r\n    { langCode: 'de', langName: 'German', isAutoGenerated: true },\r\n    { langCode: 'zh-Hans', langName: 'Chinese (Simplified)', isAutoGenerated: true },\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AAMO,MAAM,oBAAmC;IAC9C,IAAI;IACJ,OAAO;IACP,aACE,yEACA,gEACA,qGACA,0BACA,mDACA,kDACA,oDACA,kDACA,kDACA;IACF,UAAU;IACV,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,cAAc;IACd,YAAY;QACV;YAAE,QAAQ;YAAO,KAAK;QAAuD;QAC7E;YAAE,QAAQ;YAAO,KAAK;QAAuD;KAC9E;IACD,cAAc;QACZ;YAAE,cAAc;YAAY,YAAY;YAAa,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAO,YAAY;QAAK;QAClH;YAAE,cAAc;YAAQ,YAAY;YAAY,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAO,YAAY;QAAK;QAC7G;YAAE,cAAc;YAAQ,YAAY;YAAW,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAQ,YAAY;QAAK;KAC9G;IACD,cAAc;QACZ;YAAE,cAAc;YAAQ,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAK;QACjH;YAAE,cAAc;YAAU,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAQ,SAAS;YAAK,YAAY;QAAK;QACpH;YAAE,cAAc;YAAe,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAS;QAC5H;YAAE,cAAc;YAAe,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAS;KAC7H;IACD,WAAW;QACT;YAAE,UAAU;YAAM,UAAU;YAAW,iBAAiB;QAAM;QAC9D;YAAE,UAAU;YAAM,UAAU;YAAW,iBAAiB;QAAM;QAC9D;YAAE,UAAU;YAAM,UAAU;YAAU,iBAAiB;QAAK;QAC5D;YAAE,UAAU;YAAM,UAAU;YAAU,iBAAiB;QAAK;QAC5D;YAAE,UAAU;YAAW,UAAU;YAAwB,iBAAiB;QAAK;KAChF;AACH", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/handlers.ts"], "sourcesContent": ["import { http, HttpResponse } from 'msw';\r\nimport { delay } from 'msw';\r\nimport { mockVideoPageData } from './data/video-page-data';\r\n\r\nexport const handlers = [\r\n  // 示例: 拦截一个 GET 请求\r\n  http.get('/api/user', () => {\r\n    return HttpResponse.json({\r\n      id: 'c7b3d8e0-5e0b-4b0f-8b3a-3b9f4b3d3b3d',\r\n      firstName: 'John',\r\n      lastName: 'Maverick',\r\n    });\r\n  }),\r\n\r\n  // 模拟获取视频元数据的 API - 支持服务器组件和客户端组件调用\r\n  http.get('http://localhost:3000/api/youtube/metadata', async ({ request }) => {\r\n    const url = new URL(request.url);\r\n    const videoId = url.searchParams.get('videoId');\r\n\r\n    console.log(`🔶 MSW intercepted (absolute): ${request.url}`);\r\n    await delay(500);\r\n\r\n    if (videoId === 'error') {\r\n      return new HttpResponse(null, { status: 404, statusText: 'Not Found' });\r\n    }\r\n\r\n    if (videoId === 'dQw4w9WgXcQ') {\r\n      return HttpResponse.json(mockVideoPageData);\r\n    }\r\n\r\n    // 对于任何其他 videoId，也返回成功的数据\r\n    return HttpResponse.json({\r\n      ...mockVideoPageData,\r\n      id: videoId,\r\n      title: `(Mocked) Video Title for ${videoId}`,\r\n    });\r\n  }),\r\n\r\n  // 同时支持相对路径调用（客户端组件）\r\n  http.get('/api/youtube/metadata', async ({ request }) => {\r\n    const url = new URL(request.url);\r\n    const videoId = url.searchParams.get('videoId');\r\n\r\n    console.log(`🔶 MSW intercepted (relative): ${request.url}`);\r\n    await delay(500);\r\n\r\n    if (videoId === 'error') {\r\n      return new HttpResponse(null, { status: 404, statusText: 'Not Found' });\r\n    }\r\n\r\n    if (videoId === 'dQw4w9WgXcQ') {\r\n      return HttpResponse.json(mockVideoPageData);\r\n    }\r\n\r\n    // 对于任何其他 videoId，也返回成功的数据\r\n    return HttpResponse.json({\r\n      ...mockVideoPageData,\r\n      id: videoId,\r\n      title: `(Mocked) Video Title for ${videoId}`,\r\n    });\r\n  }),\r\n\r\n  // 模拟创建批量作业的 API - 绝对路径（服务器组件）\r\n  http.post('http://localhost:3000/api/batch-jobs', async ({ request }) => {\r\n    console.log(`🔶 MSW intercepted (absolute): POST ${request.url}`);\r\n    await delay(1500);\r\n\r\n    return HttpResponse.json({\r\n      batchJobId: crypto.randomUUID(),\r\n    });\r\n  }),\r\n\r\n  // 模拟创建批量作业的 API - 相对路径（客户端组件）\r\n  http.post('/api/batch-jobs', async ({ request }) => {\r\n    console.log(`🔶 MSW intercepted (relative): POST ${request.url}`);\r\n    await delay(1500);\r\n\r\n    return HttpResponse.json({\r\n      batchJobId: crypto.randomUUID(),\r\n    });\r\n  }),\r\n];"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;AAEO,MAAM,WAAW;IACtB,kBAAkB;IAClB,mJAAI,CAAC,GAAG,CAAC,aAAa;QACpB,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,IAAI;YACJ,WAAW;YACX,UAAU;QACZ;IACF;IAEA,mCAAmC;IACnC,mJAAI,CAAC,GAAG,CAAC,8CAA8C,OAAO,EAAE,OAAO,EAAE;QACvE,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,UAAU,IAAI,YAAY,CAAC,GAAG,CAAC;QAErC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,QAAQ,GAAG,EAAE;QAC3D,MAAM,IAAA,qJAAK,EAAC;QAEZ,IAAI,YAAY,SAAS;YACvB,OAAO,IAAI,mKAAY,CAAC,MAAM;gBAAE,QAAQ;gBAAK,YAAY;YAAY;QACvE;QAEA,IAAI,YAAY,eAAe;YAC7B,OAAO,mKAAY,CAAC,IAAI,CAAC,2JAAiB;QAC5C;QAEA,0BAA0B;QAC1B,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,GAAG,2JAAiB;YACpB,IAAI;YACJ,OAAO,CAAC,yBAAyB,EAAE,SAAS;QAC9C;IACF;IAEA,oBAAoB;IACpB,mJAAI,CAAC,GAAG,CAAC,yBAAyB,OAAO,EAAE,OAAO,EAAE;QAClD,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,UAAU,IAAI,YAAY,CAAC,GAAG,CAAC;QAErC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,QAAQ,GAAG,EAAE;QAC3D,MAAM,IAAA,qJAAK,EAAC;QAEZ,IAAI,YAAY,SAAS;YACvB,OAAO,IAAI,mKAAY,CAAC,MAAM;gBAAE,QAAQ;gBAAK,YAAY;YAAY;QACvE;QAEA,IAAI,YAAY,eAAe;YAC7B,OAAO,mKAAY,CAAC,IAAI,CAAC,2JAAiB;QAC5C;QAEA,0BAA0B;QAC1B,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,GAAG,2JAAiB;YACpB,IAAI;YACJ,OAAO,CAAC,yBAAyB,EAAE,SAAS;QAC9C;IACF;IAEA,8BAA8B;IAC9B,mJAAI,CAAC,IAAI,CAAC,wCAAwC,OAAO,EAAE,OAAO,EAAE;QAClE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,GAAG,EAAE;QAChE,MAAM,IAAA,qJAAK,EAAC;QAEZ,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,YAAY,OAAO,UAAU;QAC/B;IACF;IAEA,8BAA8B;IAC9B,mJAAI,CAAC,IAAI,CAAC,mBAAmB,OAAO,EAAE,OAAO,EAAE;QAC7C,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,GAAG,EAAE;QAChE,MAAM,IAAA,qJAAK,EAAC;QAEZ,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,YAAY,OAAO,UAAU;QAC/B;IACF;CACD", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/browser.ts"], "sourcesContent": ["import { setupWorker } from 'msw/browser';\r\nimport { handlers } from './handlers';\r\n\r\n// This configures a Service Worker with the given request handlers.\r\nexport const worker = setupWorker(...handlers);"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,SAAS,IAAA,8JAAW,KAAI,6HAAQ", "debugId": null}}]}