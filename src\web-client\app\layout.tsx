import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "next/font/google";
import type { Metadata } from "next";

import { MSWComponent } from "@/components/shared/msw-component";
import "./globals.css";



const geistSans = GeistSans({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = GeistMono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "YTDownloader - Professional YouTube Content Downloader",
  description:
    "Download YouTube videos, playlists, channels, subtitles, and more. Supports online trimming and format conversion.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased`}
      >
        <MSWComponent />
        {children}
      </body>
    </html>
  );
}
