{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/header.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container mx-auto flex h-14 items-center px-4\">\r\n        <div className=\"flex flex-1 items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n              <DownloadCloud className=\"h-6 w-6\" />\r\n              <span className=\"font-bold sm:inline-block\">YTDownloader</span>\r\n            </Link>\r\n          </div>\r\n          <nav className=\"hidden items-center space-x-6 text-sm md:flex\">\r\n            <Link\r\n              href=\"/#features\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              功能\r\n            </Link>\r\n            <Link\r\n              href=\"/pricing\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              价格\r\n            </Link>\r\n            <Link\r\n              href=\"/#faq\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              FAQ\r\n            </Link>\r\n            <div className=\"w-px h-6 bg-border\" />\r\n            <Button variant=\"ghost\">登录</Button>\r\n            <Button>注册</Button>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC,qIAAM;gCAAC,SAAQ;0CAAQ;;;;;;0CACxB,8OAAC,qIAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Footer() {\r\n  return (\r\n    <footer className=\"border-t\">\r\n      <div className=\"container mx-auto space-y-4 py-8 px-4 text-center md:flex md:items-center md:justify-between md:space-y-0 md:py-4\">\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          <DownloadCloud className=\"h-5 w-5\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            &copy; {new Date().getFullYear()} YTDownloader. All rights reserved.\r\n          </span>\r\n        </div>\r\n        <div className=\"text-center text-sm text-muted-foreground md:text-left\">\r\n          <p>\r\n            免责声明: 本服务仅供个人学习和研究使用，请勿用于商业用途。用户需自行承担因使用本服务而产生的版权风险。\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center justify-center space-x-4 text-sm font-medium text-muted-foreground\">\r\n          <Link href=\"/terms\" className=\"hover:text-foreground\">\r\n            服务条款\r\n          </Link>\r\n          <Link href=\"/privacy\" className=\"hover:text-foreground\">\r\n            隐私政策\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yOAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;;gCAAgC;gCACtC,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;8BAIL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAwB;;;;;;sCAGtD,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/pricing/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\nimport { Footer } from \"@/components/layout/footer\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Check, X, Star } from \"lucide-react\";\n\nexport const metadata = {\n  title: \"价格方案 - YTDownloader\",\n  description: \"选择适合您的 YTDownloader 订阅方案\",\n};\n\nconst plans = [\n  {\n    name: \"免费版\",\n    price: \"¥0\",\n    period: \"永久免费\",\n    description: \"适合个人轻度使用\",\n    popular: false,\n    features: [\n      { name: \"视频质量\", value: \"最高 1080p\", included: true },\n      { name: \"音频质量\", value: \"最高 192kbps\", included: true },\n      { name: \"批量下载\", value: \"最多 20 个视频\", included: true },\n      { name: \"评论下载\", value: \"最多 100 条\", included: true },\n      { name: \"字幕下载\", value: \"支持\", included: true },\n      { name: \"在线剪辑\", value: \"基础功能\", included: true },\n      { name: \"并发任务\", value: \"1 个\", included: true },\n      { name: \"文件保留\", value: \"24 小时\", included: true },\n      { name: \"4K/8K 下载\", value: \"不支持\", included: false },\n      { name: \"高级音频格式\", value: \"不支持\", included: false },\n      { name: \"优先处理\", value: \"不支持\", included: false },\n      { name: \"技术支持\", value: \"社区支持\", included: true },\n    ],\n    buttonText: \"开始使用\",\n    buttonVariant: \"outline\" as const,\n  },\n  {\n    name: \"专业版\",\n    price: \"¥29\",\n    period: \"每月\",\n    description: \"适合专业用户和内容创作者\",\n    popular: true,\n    features: [\n      { name: \"视频质量\", value: \"无限制（最高 8K）\", included: true },\n      { name: \"音频质量\", value: \"最高 320kbps + 原始格式\", included: true },\n      { name: \"批量下载\", value: \"最多 1000 个视频\", included: true },\n      { name: \"评论下载\", value: \"最多 1000 条\", included: true },\n      { name: \"字幕下载\", value: \"支持 + 多语言制作\", included: true },\n      { name: \"在线剪辑\", value: \"高级功能\", included: true },\n      { name: \"并发任务\", value: \"5 个\", included: true },\n      { name: \"文件保留\", value: \"7 天\", included: true },\n      { name: \"4K/8K 下载\", value: \"支持\", included: true },\n      { name: \"高级音频格式\", value: \"M4A/FLAC\", included: true },\n      { name: \"优先处理\", value: \"支持\", included: true },\n      { name: \"技术支持\", value: \"优先邮件支持\", included: true },\n    ],\n    buttonText: \"立即升级\",\n    buttonVariant: \"default\" as const,\n  },\n];\n\nconst faqs = [\n  {\n    question: \"可以随时取消订阅吗？\",\n    answer: \"是的，您可以随时取消专业版订阅。取消后，您的账户将在当前计费周期结束时自动降级为免费版。\",\n  },\n  {\n    question: \"支持哪些支付方式？\",\n    answer: \"我们支持支付宝、微信支付、银行卡等多种支付方式。所有支付都通过安全的第三方支付平台处理。\",\n  },\n  {\n    question: \"专业版有试用期吗？\",\n    answer: \"新用户可以享受7天免费试用专业版功能。试用期结束前，您可以选择订阅或取消，不会自动扣费。\",\n  },\n  {\n    question: \"下载的文件有版权问题吗？\",\n    answer: \"用户需自行承担版权责任。我们建议仅下载您有权使用的内容，或用于个人学习研究等合理使用范围内。\",\n  },\n];\n\nexport default function PricingPage() {\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <Header />\n      \n      <main className=\"flex-grow\">\n        {/* Hero Section */}\n        <section className=\"container mx-auto px-4 py-16 text-center\">\n          <h1 className=\"text-4xl font-bold tracking-tight md:text-6xl\">\n            选择适合您的方案\n          </h1>\n          <p className=\"mt-4 max-w-2xl mx-auto text-lg text-muted-foreground\">\n            从免费版开始体验，随时升级到专业版享受更多高级功能\n          </p>\n        </section>\n\n        {/* Pricing Cards */}\n        <section className=\"container mx-auto px-4 pb-16\">\n          <div className=\"grid gap-8 md:grid-cols-2 max-w-4xl mx-auto\">\n            {plans.map((plan) => (\n              <Card key={plan.name} className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''}`}>\n                {plan.popular && (\n                  <Badge className=\"absolute -top-3 left-1/2 -translate-x-1/2 bg-primary\">\n                    <Star className=\"mr-1 h-3 w-3\" />\n                    最受欢迎\n                  </Badge>\n                )}\n                <CardHeader className=\"text-center\">\n                  <CardTitle className=\"text-2xl\">{plan.name}</CardTitle>\n                  <div className=\"mt-4\">\n                    <span className=\"text-4xl font-bold\">{plan.price}</span>\n                    <span className=\"text-muted-foreground\">/{plan.period}</span>\n                  </div>\n                  <p className=\"text-muted-foreground\">{plan.description}</p>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <Button \n                    className=\"w-full\" \n                    variant={plan.buttonVariant}\n                    size=\"lg\"\n                  >\n                    {plan.buttonText}\n                  </Button>\n                  \n                  <div className=\"space-y-3\">\n                    {plan.features.map((feature) => (\n                      <div key={feature.name} className=\"flex items-start gap-3\">\n                        {feature.included ? (\n                          <Check className=\"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                        ) : (\n                          <X className=\"h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0\" />\n                        )}\n                        <div className=\"flex-1\">\n                          <span className=\"font-medium\">{feature.name}：</span>\n                          <span className={feature.included ? \"text-foreground\" : \"text-muted-foreground\"}>\n                            {feature.value}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"bg-muted/50 py-16\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"max-w-3xl mx-auto\">\n              <h2 className=\"text-3xl font-bold text-center mb-12\">常见问题</h2>\n              <div className=\"space-y-6\">\n                {faqs.map((faq, index) => (\n                  <Card key={index}>\n                    <CardHeader>\n                      <CardTitle className=\"text-lg\">{faq.question}</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <p className=\"text-muted-foreground\">{faq.answer}</p>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"container mx-auto px-4 py-16 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">准备开始了吗？</h2>\n          <p className=\"text-lg text-muted-foreground mb-8 max-w-2xl mx-auto\">\n            立即注册免费账户，体验专业的YouTube内容下载服务\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Button size=\"lg\">免费注册</Button>\n            <Button variant=\"outline\" size=\"lg\">联系销售</Button>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAQ,OAAO;gBAAY,UAAU;YAAK;YAClD;gBAAE,MAAM;gBAAQ,OAAO;gBAAc,UAAU;YAAK;YACpD;gBAAE,MAAM;gBAAQ,OAAO;gBAAa,UAAU;YAAK;YACnD;gBAAE,MAAM;gBAAQ,OAAO;gBAAY,UAAU;YAAK;YAClD;gBAAE,MAAM;gBAAQ,OAAO;gBAAM,UAAU;YAAK;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAQ,OAAO;gBAAS,UAAU;YAAK;YAC/C;gBAAE,MAAM;gBAAY,OAAO;gBAAO,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAU,OAAO;gBAAO,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,UAAU;YAAM;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,UAAU;YAAK;SAC/C;QACD,YAAY;QACZ,eAAe;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAQ,OAAO;gBAAc,UAAU;YAAK;YACpD;gBAAE,MAAM;gBAAQ,OAAO;gBAAqB,UAAU;YAAK;YAC3D;gBAAE,MAAM;gBAAQ,OAAO;gBAAe,UAAU;YAAK;YACrD;gBAAE,MAAM;gBAAQ,OAAO;gBAAa,UAAU;YAAK;YACnD;gBAAE,MAAM;gBAAQ,OAAO;gBAAc,UAAU;YAAK;YACpD;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAY,OAAO;gBAAM,UAAU;YAAK;YAChD;gBAAE,MAAM;gBAAU,OAAO;gBAAY,UAAU;YAAK;YACpD;gBAAE,MAAM;gBAAQ,OAAO;gBAAM,UAAU;YAAK;YAC5C;gBAAE,MAAM;gBAAQ,OAAO;gBAAU,UAAU;YAAK;SACjD;QACD,YAAY;QACZ,eAAe;IACjB;CACD;AAED,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAAuD;;;;;;;;;;;;kCAMtE,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,iIAAI;oCAAiB,WAAW,CAAC,SAAS,EAAE,KAAK,OAAO,GAAG,6BAA6B,IAAI;;wCAC1F,KAAK,OAAO,kBACX,8OAAC,mIAAK;4CAAC,WAAU;;8DACf,8OAAC,0MAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIrC,8OAAC,uIAAU;4CAAC,WAAU;;8DACpB,8OAAC,sIAAS;oDAAC,WAAU;8DAAY,KAAK,IAAI;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsB,KAAK,KAAK;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;gEAAwB;gEAAE,KAAK,MAAM;;;;;;;;;;;;;8DAEvD,8OAAC;oDAAE,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;sDAExD,8OAAC,wIAAW;4CAAC,WAAU;;8DACrB,8OAAC,qIAAM;oDACL,WAAU;oDACV,SAAS,KAAK,aAAa;oDAC3B,MAAK;8DAEJ,KAAK,UAAU;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC;4DAAuB,WAAU;;gEAC/B,QAAQ,QAAQ,iBACf,8OAAC,6MAAK;oEAAC,WAAU;;;;;yFAEjB,8OAAC,iMAAC;oEAAC,WAAU;;;;;;8EAEf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;gFAAe,QAAQ,IAAI;gFAAC;;;;;;;sFAC5C,8OAAC;4EAAK,WAAW,QAAQ,QAAQ,GAAG,oBAAoB;sFACrD,QAAQ,KAAK;;;;;;;;;;;;;2DATV,QAAQ,IAAI;;;;;;;;;;;;;;;;;mCA1BnB,KAAK,IAAI;;;;;;;;;;;;;;;kCAgD1B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,iIAAI;;kEACH,8OAAC,uIAAU;kEACT,cAAA,8OAAC,sIAAS;4DAAC,WAAU;sEAAW,IAAI,QAAQ;;;;;;;;;;;kEAE9C,8OAAC,wIAAW;kEACV,cAAA,8OAAC;4DAAE,WAAU;sEAAyB,IAAI,MAAM;;;;;;;;;;;;+CALzC;;;;;;;;;;;;;;;;;;;;;;;;;;kCAerB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAuD;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAM;wCAAC,MAAK;kDAAK;;;;;;kDAClB,8OAAC,qIAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC,yIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}