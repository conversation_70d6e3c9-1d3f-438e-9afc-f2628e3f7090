import { setupServer } from 'msw/node';
import { handlers } from './handlers';

// 创建服务器端MSW实例
export const server = setupServer(...handlers);

// 启动服务器端拦截器
export function startServer() {
  server.listen({
    onUnhandledRequest: 'bypass',
  });
  console.log('🔶 MSW server started for Node.js (server-side)');
}

// 停止服务器端拦截器
export function stopServer() {
  server.close();
  console.log('🔶 MSW server stopped');
}

// 重置处理器
export function resetServer() {
  server.resetHandlers();
}
