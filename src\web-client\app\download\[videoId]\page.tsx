import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { AlertTriangle, Home } from "lucide-react";
import { VideoPageData } from "@/lib/types/definitions";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { VideoDescription } from "@/components/features/video-description";
import { CommentDownloader } from "@/components/features/comment-downloader";
import { DownloadOptionsTabs } from "@/components/features/download-options-tabs";
import { ThumbnailDownloader } from "@/components/features/thumbnail-downloader";

async function getVideoData(videoId: string): Promise<VideoPageData | null> {
  try {
    // 在 RSC 中直接使用 fetch，Next.js 会自动处理缓存和去重
    // 我们用 tag 来方便地按需重新验证
    const res = await fetch(`http://localhost:3000/api/youtube/metadata?videoId=${videoId}`, {
      next: { tags: ['video-metadata', videoId] },
    });

    if (!res.ok) {
      return null;
    }
    return res.json();
  } catch (error) {
    console.error("Failed to fetch video data:", error);
    return null;
  }
}

export default async function DownloadPage({
  params,
}: {
  params: Promise<{ videoId: string }>;
}) {
  const { videoId } = await params;
  const videoData = await getVideoData(videoId);

  // 如果 videoData 为 null，可以渲染一个错误组件或调用 notFound()
  if (!videoData) {
    // return notFound(); // 或者渲染下面的自定义错误组件
    return (
        <div className="flex min-h-screen flex-col">
            <Header />
            <main className="flex flex-grow items-center justify-center">
                <div className="flex flex-col items-center gap-4 rounded-lg border p-8 text-center">
                    <AlertTriangle className="h-12 w-12 text-destructive" />
                    <h1 className="text-2xl font-bold">无法加载此视频</h1>
                    <p className="max-w-sm text-muted-foreground">
                        该视频可能已被删除、设为私有，或存在区域限制。请检查链接是否正确。
                    </p>
                    <Button asChild>
                        <Link href="/">
                            <Home className="mr-2 h-4 w-4" />
                            返回首页
                        </Link>
                    </Button>
                </div>
            </main>
            <Footer />
        </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-grow bg-muted/20">
        <div className="container mx-auto grid max-w-6xl grid-cols-1 gap-8 px-4 py-8 md:grid-cols-3">
          {/* 左侧区域: 视频信息概览 */}
          <aside className="md:col-span-1">
            <div className="sticky top-20 space-y-6">
              {/* 视频缩略图 */}
              <div className="space-y-3">
                <div className="overflow-hidden rounded-lg">
                  <Image
                    src={videoData.thumbnailUrl}
                    alt={`Thumbnail for ${videoData.title}`}
                    width={1280}
                    height={720}
                    className="aspect-video w-full object-cover"
                    priority
                  />
                </div>
                <ThumbnailDownloader videoData={videoData} />
              </div>

              {/* 核心元数据 */}
              <div className="space-y-3">
                <h1 className="text-2xl font-bold leading-tight" title={videoData.title}>
                  {videoData.title}
                </h1>
                <p className="text-sm font-medium text-muted-foreground">
                  by{' '}
                  <a href={videoData.channelUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    {videoData.channelName}
                  </a>
                </p>
                <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground">
                  <span>{new Date(videoData.uploadDate).toLocaleDateString()}</span>
                  <span>{videoData.viewCount.toLocaleString()} 次观看</span>
                  <span>{videoData.commentCount.toLocaleString()} 条评论</span>
                </div>
              </div>

              {/* 视频描述 */}
              <VideoDescription description={videoData.description} />
              
              {/* 评论下载 */}
              <div className="space-y-4 rounded-lg border p-4">
                 <h3 className="font-semibold">下载评论</h3>
                 <CommentDownloader commentsDisabled={videoData.commentsDisabled} />
              </div>

            </div>
          </aside>

          {/* 右侧区域: 下载与处理选项 */}
          <section className="md:col-span-2">
            <DownloadOptionsTabs videoData={videoData} />
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
}