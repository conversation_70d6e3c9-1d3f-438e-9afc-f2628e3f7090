import Link from "next/link";
import { DownloadCloud } from "lucide-react";

export function Footer() {
  return (
    <footer className="border-t">
      <div className="container space-y-4 py-8 text-center md:flex md:items-center md:justify-between md:space-y-0 md:py-4">
        <div className="flex items-center justify-center space-x-2">
          <DownloadCloud className="h-5 w-5" />
          <span className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} YTDownloader. All rights reserved.
          </span>
        </div>
        <div className="text-center text-sm text-muted-foreground md:text-left">
          <p>
            免责声明: 本服务仅供个人学习和研究使用，请勿用于商业用途。用户需自行承担因使用本服务而产生的版权风险。
          </p>
        </div>
        <div className="flex items-center justify-center space-x-4 text-sm font-medium text-muted-foreground">
          <Link href="/terms" className="hover:text-foreground">
            服务条款
          </Link>
          <Link href="/privacy" className="hover:text-foreground">
            隐私政策
          </Link>
        </div>
      </div>
    </footer>
  );
}