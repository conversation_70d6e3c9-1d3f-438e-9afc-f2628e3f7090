{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/header.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container mx-auto flex h-14 items-center px-4\">\r\n        <div className=\"flex flex-1 items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n              <DownloadCloud className=\"h-6 w-6\" />\r\n              <span className=\"font-bold sm:inline-block\">YTDownloader</span>\r\n            </Link>\r\n          </div>\r\n          <nav className=\"hidden items-center space-x-6 text-sm md:flex\">\r\n            <Link\r\n              href=\"/#features\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              功能\r\n            </Link>\r\n            <Link\r\n              href=\"/pricing\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              价格\r\n            </Link>\r\n            <Link\r\n              href=\"/#faq\"\r\n              className=\"text-foreground/60 transition-colors hover:text-foreground/80\"\r\n            >\r\n              FAQ\r\n            </Link>\r\n            <div className=\"w-px h-6 bg-border\" />\r\n            <Button variant=\"ghost\">登录</Button>\r\n            <Button>注册</Button>\r\n          </nav>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,yOAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC,qIAAM;gCAAC,SAAQ;0CAAQ;;;;;;0CACxB,8OAAC,qIAAM;0CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { DownloadCloud } from \"lucide-react\";\r\n\r\nexport function Footer() {\r\n  return (\r\n    <footer className=\"border-t\">\r\n      <div className=\"container mx-auto space-y-4 py-8 px-4 text-center md:flex md:items-center md:justify-between md:space-y-0 md:py-4\">\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          <DownloadCloud className=\"h-5 w-5\" />\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            &copy; {new Date().getFullYear()} YTDownloader. All rights reserved.\r\n          </span>\r\n        </div>\r\n        <div className=\"text-center text-sm text-muted-foreground md:text-left\">\r\n          <p>\r\n            免责声明: 本服务仅供个人学习和研究使用，请勿用于商业用途。用户需自行承担因使用本服务而产生的版权风险。\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center justify-center space-x-4 text-sm font-medium text-muted-foreground\">\r\n          <Link href=\"/terms\" className=\"hover:text-foreground\">\r\n            服务条款\r\n          </Link>\r\n          <Link href=\"/privacy\" className=\"hover:text-foreground\">\r\n            隐私政策\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yOAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;;gCAAgC;gCACtC,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;8BAGrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;8BAIL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BAAC,MAAK;4BAAS,WAAU;sCAAwB;;;;;;sCAGtD,8OAAC,uKAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,kHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/download/%5BvideoId%5D/loading.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\";\r\nimport { Footer } from \"@/components/layout/footer\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function Loading() {\r\n  return (\r\n    <div className=\"flex min-h-screen flex-col\">\r\n      <Header />\r\n      <main className=\"flex-grow bg-muted/20\">\r\n        <div className=\"container mx-auto grid max-w-6xl grid-cols-1 gap-8 px-4 py-8 md:grid-cols-3\">\r\n          {/* 左侧区域: 骨架屏 */}\r\n          <aside className=\"md:col-span-1\">\r\n            <div className=\"sticky top-20 space-y-6\">\r\n              <Skeleton className=\"aspect-video w-full rounded-lg\" />\r\n              <div className=\"space-y-2\">\r\n                <Skeleton className=\"h-6 w-full\" />\r\n                <Skeleton className=\"h-6 w-3/4\" />\r\n              </div>\r\n              <Skeleton className=\"h-8 w-1/2\" />\r\n              <div className=\"space-y-2\">\r\n                  <Skeleton className=\"h-4 w-full\" />\r\n                  <Skeleton className=\"h-4 w-full\" />\r\n                  <Skeleton className=\"h-4 w-5/6\" />\r\n              </div>\r\n            </div>\r\n          </aside>\r\n\r\n          {/* 右侧区域: 骨架屏 */}\r\n          <section className=\"space-y-6 md:col-span-2\">\r\n            <Skeleton className=\"h-12 w-full\" />\r\n            <div className=\"space-y-4 rounded-lg border p-4\">\r\n                 <Skeleton className=\"h-8 w-full\" />\r\n                 <Skeleton className=\"h-8 w-full\" />\r\n                 <Skeleton className=\"h-8 w-full\" />\r\n            </div>\r\n          </section>\r\n        </div>\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,yIAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,yIAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,yIAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,yIAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,yIAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,yIAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,yIAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC,yIAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACV,8OAAC,yIAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,yIAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,yIAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK/B,8OAAC,yIAAM;;;;;;;;;;;AAGb", "debugId": null}}]}