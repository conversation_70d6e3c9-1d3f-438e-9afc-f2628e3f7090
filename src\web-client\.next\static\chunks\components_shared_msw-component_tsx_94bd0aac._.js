(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/components/shared/msw-component.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MSWComponent",
    ()=>MSWComponent
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function MSWComponent() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MSWComponent.useEffect": ()=>{
            async function initMSW() {
                if ("TURBOPACK compile-time truthy", 1) {
                    const { worker } = await __turbopack_context__.A("[project]/mocks/browser.ts [app-client] (ecmascript, async loader)");
                    await worker.start({
                        onUnhandledRequest: 'bypass'
                    });
                    console.log('🔶 MSW worker started for browser (client-side)');
                }
            }
            initMSW();
        }
    }["MSWComponent.useEffect"], []);
    return null;
}
_s(MSWComponent, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = MSWComponent;
var _c;
__turbopack_context__.k.register(_c, "MSWComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=components_shared_msw-component_tsx_94bd0aac._.js.map