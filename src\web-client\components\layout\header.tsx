import Link from "next/link";
import { Button } from "@/components/ui/button";
import { DownloadCloud } from "lucide-react";

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto flex h-14 items-center px-4">
        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center space-x-2">
              <DownloadCloud className="h-6 w-6" />
              <span className="font-bold sm:inline-block">YTDownloader</span>
            </Link>
          </div>
          <nav className="hidden items-center space-x-6 text-sm md:flex">
            <Link
              href="/#features"
              className="text-foreground/60 transition-colors hover:text-foreground/80"
            >
              功能
            </Link>
            <Link
              href="/pricing"
              className="text-foreground/60 transition-colors hover:text-foreground/80"
            >
              价格
            </Link>
            <Link
              href="/#faq"
              className="text-foreground/60 transition-colors hover:text-foreground/80"
            >
              FAQ
            </Link>
            <div className="w-px h-6 bg-border" />
            <Button variant="ghost">登录</Button>
            <Button>注册</Button>
          </nav>
        </div>
      </div>
    </header>
  );
}