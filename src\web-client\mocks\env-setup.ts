/**
 * MSW 环境设置工具
 * 确保在正确的环境中初始化正确的MSW实例
 */

export function isServer() {
  return typeof window === 'undefined';
}

export function isClient() {
  return typeof window !== 'undefined';
}

export function isDevelopment() {
  return process.env.NODE_ENV === 'development';
}

export function shouldInitializeServerMSW() {
  return isDevelopment() && isServer();
}

export function shouldInitializeClientMSW() {
  return isDevelopment() && isClient();
}

// 调试信息
export function logEnvironmentInfo() {
  console.log('🔍 MSW Environment Info:', {
    isDevelopment: isDevelopment(),
    isServer: isServer(),
    isClient: isClient(),
    shouldInitServer: shouldInitializeServerMSW(),
    shouldInitClient: shouldInitializeClientMSW(),
  });
}
