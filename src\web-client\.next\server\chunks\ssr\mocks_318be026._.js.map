{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/handlers.ts"], "sourcesContent": ["import { http, HttpResponse } from 'msw';\r\n\r\nexport const handlers = [\r\n  // 示例: 拦截一个 GET 请求\r\n  http.get('/api/user', () => {\r\n    return HttpResponse.json({\r\n      id: 'c7b3d8e0-5e0b-4b0f-8b3a-3b9f4b3d3b3d',\r\n      firstName: 'John',\r\n      lastName: 'Maverick',\r\n    });\r\n  }),\r\n\r\n  // 注意：现在服务器组件的API调用由实际的API路由处理\r\n  // MSW只用于客户端组件的模拟（如果需要的话）\r\n];"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,MAAM,WAAW;IACtB,kBAAkB;IAClB,mJAAI,CAAC,GAAG,CAAC,aAAa;QACpB,OAAO,mKAAY,CAAC,IAAI,CAAC;YACvB,IAAI;YACJ,WAAW;YACX,UAAU;QACZ;IACF;CAID", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/browser.ts"], "sourcesContent": ["import { setupWorker } from 'msw/browser';\r\nimport { handlers } from './handlers';\r\n\r\n// This configures a Service Worker with the given request handlers.\r\nexport const worker = setupWorker(...handlers);"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,SAAS,IAAA,8JAAW,KAAI,6HAAQ", "debugId": null}}]}