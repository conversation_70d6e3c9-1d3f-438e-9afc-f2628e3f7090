/**
 * 核心实体与 API 数据结构的 TypeScript 定义。
 * 来源: docs/project.md
 */

// =================================================================================
// 2.1.1. 工作任务 (WorkTask)
// =================================================================================

export type WorkTaskType =
  | 'VIDEO_DOWNLOAD'
  | 'AUDIO_CONVERT'
  | 'GIF_CREATE'
  | 'SUBTITLE_DOWNLOAD'
  | 'COMMENT_DOWNLOAD'
  | 'THUMBNAIL_DOWNLOAD'
  | 'METADATA_DOWNLOAD';

export type WorkTaskStatus =
  | 'pending'
  | 'queued'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface WorkTask {
  /** 全局唯一标识符 */
  id: string;
  /** 任务发起者用户 ID */
  userId: string;
  /** 关联的父批量作业 ID (若为单次任务，则为 null) */
  batchJobId: string | null;
  /** 操作类型 */
  type: WorkTaskType;
  /** 任务具体参数 (JSON 字符串或对象) */
  params: Record<string, unknown> | string;
  /** 任务状态 */
  status: WorkTaskStatus;
  /** 完成百分比 (0-100) */
  progress: number;
  /** 任务成功完成后的结果 */
  result: Record<string, unknown> | null;
  /** 失败时的错误描述 */
  error: string | null;
  /** 创建时间 (ISO 8601 字符串) */
  createdAt: string;
  /** 开始处理时间 (ISO 8601 字符串) */
  startedAt: string | null;
  /** 结束时间 (ISO 8601 字符串) */
  finishedAt: string | null;
}

// =================================================================================
// 2.1.2. 批量作业 (BatchJob)
// =================================================================================

export type BatchJobSourceType =
  | 'PLAYLIST'
  | 'CHANNEL'
  | 'MULTI_VIDEO_LIST';

export type BatchJobStatus =
  | 'pending'
  | 'running'
  | 'partially_completed'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface BatchJob {
  /** 全局唯一标识符 */
  id: string;
  /** 任务发起者用户 ID */
  userId: string;
  /** 输入源类型 */
  sourceType: BatchJobSourceType;
  /** 输入源标识 (JSON 字符串或对象) */
  sourceIdentifier: Record<string, unknown> | string;
  /** 全局下载配置 (JSON 字符串或对象) */
  config: Record<string, unknown> | string;
  /** 聚合的整体状态 */
  status: BatchJobStatus;
  /** 完成百分比 (0-100) */
  progress: number;
  /** 创建时间 (ISO 8601 字符串) */
  createdAt: string;
  /** 开始处理时间 (ISO 8601 字符串) */
  startedAt: string | null;
  /** 结束时间 (ISO 8601 字符串) */
  finishedAt: string | null;
  /** 关联的工作任务列表 */
  tasks?: WorkTask[];
}
// =================================================================================
// 3.1.2. 单次下载页 (/download/{videoId}) - API 响应
// =================================================================================

/** 视频元数据 */
export interface VideoMetadata {
  /** 视频 ID */
  id: string;
  /** 视频标题 */
  title: string;
  /** 视频描述 */
  description: string;
  /** 视频总时长 (秒) */
  duration: number;
  /** 频道名称 */
  channelName: string;
  /** 频道 URL */
  channelUrl: string;
  /** 上传日期 (ISO 8601 字符串) */
  uploadDate: string;
  /** 观看次数 */
  viewCount: number;
  /** 评论数量 */
  commentCount: number;
  /** 评论是否被禁用 */
  commentsDisabled: boolean;
  /** 最高质量的缩略图 URL */
  thumbnailUrl: string;
  /** 可用的缩略图格式 */
  thumbnails: { format: 'jpg' | 'png'; url: string }[];
}

/** 可用的文件流信息 */
export interface MediaStream {
  /** 质量等级标签 (e.g., "1080p HD") */
  qualityLabel: string;
  /** 分辨率 (e.g., "1920x1080") */
  resolution: string;
  /** 帧率 (FPS) */
  fps: number;
  /** 预估文件大小 (字节) */
  fileSize: number;
  /** 文件格式 (e.g., "MP4", "MKV", "M4A", "WebM") */
  format: string;
  /** 音频码率 (kbps, 仅音频) */
  bitrate?: number;
  /** 下载此流所需的参数或标识 */
  downloadId: string;
}

/** 可用的字幕信息 */
export interface SubtitleInfo {
  /** 语言代码 (e.g., "en", "zh-CN") */
  langCode: string;
  /** 语言名称 (e.g., "English", "中文（简体）") */
  langName: string;
  /** 是否为自动生成 */
  isAutoGenerated: boolean;
}

/** `/api/youtube/metadata` 端点的响应体 */
export interface VideoPageData extends VideoMetadata {
  /** 可用的视频流列表 (含音频) */
  videoStreams: MediaStream[];
  /** 可用的纯音频流列表 */
  audioStreams: MediaStream[];
  /** 可用的字幕列表 */
  subtitles: SubtitleInfo[];
}