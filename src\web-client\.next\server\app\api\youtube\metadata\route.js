var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/youtube/metadata/route.js")
R.c("server/chunks/node_modules_next_d5e2e8ee._.js")
R.c("server/chunks/[root-of-the-server]__f62416c2._.js")
R.m("[project]/.next-internal/server/app/api/youtube/metadata/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/youtube/metadata/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/youtube/metadata/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
