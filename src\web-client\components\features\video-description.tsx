'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy, Download, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VideoDescriptionProps {
  description: string;
}

export function VideoDescription({ description }: VideoDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const handleCopyDescription = async () => {
    try {
      await navigator.clipboard.writeText(description);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy description:', error);
    }
  };

  const handleDownloadDescription = () => {
    const blob = new Blob([description], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'video-description.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-3">
      <div className="text-sm">
        <p
          className={cn('whitespace-pre-wrap', !isExpanded && 'line-clamp-4')}
        >
          {description}
        </p>
        <Button
          variant="link"
          size="sm"
          className="px-0"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? '收起' : '展开'}
        </Button>
      </div>

      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCopyDescription}
          disabled={isCopied}
        >
          {isCopied ? (
            <Check className="mr-2 h-4 w-4" />
          ) : (
            <Copy className="mr-2 h-4 w-4" />
          )}
          {isCopied ? '已复制' : '复制描述'}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadDescription}
        >
          <Download className="mr-2 h-4 w-4" />
          下载描述 (TXT)
        </Button>
      </div>
    </div>
  );
}