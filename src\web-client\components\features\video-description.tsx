'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface VideoDescriptionProps {
  description: string;
}

export function VideoDescription({ description }: VideoDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="text-sm">
      <p
        className={cn('whitespace-pre-wrap', !isExpanded && 'line-clamp-4')}
      >
        {description}
      </p>
      <Button
        variant="link"
        size="sm"
        className="px-0"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? '收起' : '展开'}
      </Button>
    </div>
  );
}