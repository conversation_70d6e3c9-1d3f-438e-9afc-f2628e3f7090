'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { VideoPageData } from '@/lib/types/definitions';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { bytesToSize } from '@/lib/formatters';

interface DownloadOptionsTabsProps {
  videoData: VideoPageData;
}

export function DownloadOptionsTabs({ videoData }: DownloadOptionsTabsProps) {
  const { videoStreams, audioStreams, subtitles } = videoData;

  return (
    <Tabs defaultValue="video">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="video">视频</TabsTrigger>
        <TabsTrigger value="audio">音频</TabsTrigger>
        <TabsTrigger value="subtitle">字幕</TabsTrigger>
      </TabsList>

      {/* 视频选项卡内容 */}
      <TabsContent value="video" className="mt-6">
        <div className="space-y-6">
          {/* 完整视频列表 */}
          <div className="rounded-lg border">
            <div className="p-4">
              <h3 className="text-lg font-semibold">完整视频下载</h3>
              <p className="text-sm text-muted-foreground">
                包含音频的视频文件，按质量从高到低排序。
              </p>
            </div>
            <ul className="divide-y border-t">
              {videoStreams.map((stream) => (
                <li key={stream.downloadId} className="flex items-center justify-between p-4">
                  <div className="flex flex-col gap-1">
                    <span className="font-semibold">{stream.qualityLabel}</span>
                    <span className="text-sm text-muted-foreground">
                      {stream.resolution} • {stream.fps}fps • {stream.format}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-24 text-right text-sm font-medium">
                      {bytesToSize(stream.fileSize)}
                    </span>
                    <Button size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      下载
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          {/* 视频剪辑面板 (占位) */}
          <div className="rounded-lg border p-4">
            <h3 className="text-lg font-semibold">视频剪辑与 GIF 制作</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              此功能将在后续任务中实现。
            </p>
          </div>
        </div>
      </TabsContent>

      {/* 音频选项卡内容 */}
      <TabsContent value="audio" className="mt-6">
        <div className="space-y-6">
          {/* 完整音频列表 */}
          <div className="rounded-lg border p-4">
            <h3 className="mb-4 text-lg font-semibold">完整音频下载</h3>
            <ul className="space-y-3">
              {audioStreams.map((stream) => (
                <li
                  key={stream.downloadId}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{stream.qualityLabel}</span>
                    <span className="text-sm text-muted-foreground">
                      {stream.format} • {stream.bitrate ? `${stream.bitrate}kbps • ` : ''}
                      {bytesToSize(stream.fileSize)}
                    </span>
                  </div>
                  <Button size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    下载
                  </Button>
                </li>
              ))}
            </ul>
          </div>
          {/* 音频剪辑面板 (占位) */}
          <div className="rounded-lg border p-4">
            <h3 className="text-lg font-semibold">音频剪辑与铃声制作</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              此功能将在后续任务中实现。
            </p>
          </div>
        </div>
      </TabsContent>

      {/* 字幕选项卡内容 */}
      <TabsContent value="subtitle" className="mt-6">
        <div className="space-y-6">
          {subtitles.length > 0 ? (
            <>
              {/* 原始字幕列表 */}
              <div className="rounded-lg border p-4">
                <h3 className="mb-4 text-lg font-semibold">原始字幕下载</h3>
                <div className="space-y-4">
                  {/* 官方字幕 */}
                  {subtitles.filter(sub => !sub.isAutoGenerated).length > 0 && (
                    <div>
                      <h4 className="mb-2 text-sm font-medium text-muted-foreground">官方字幕</h4>
                      <div className="space-y-2">
                        {subtitles
                          .filter(sub => !sub.isAutoGenerated)
                          .map((subtitle) => (
                            <div
                              key={subtitle.langCode}
                              className="flex items-center justify-between rounded-lg border p-3"
                            >
                              <span className="font-medium">{subtitle.langName}</span>
                              <div className="flex gap-2">
                                <Button variant="outline" size="sm">
                                  查看
                                </Button>
                                <Button size="sm">
                                  <Download className="mr-2 h-4 w-4" />
                                  下载 SRT
                                </Button>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {/* 自动生成字幕 */}
                  {subtitles.filter(sub => sub.isAutoGenerated).length > 0 && (
                    <div>
                      <h4 className="mb-2 text-sm font-medium text-muted-foreground">自动生成字幕</h4>
                      <div className="space-y-2">
                        {subtitles
                          .filter(sub => sub.isAutoGenerated)
                          .map((subtitle) => (
                            <div
                              key={subtitle.langCode}
                              className="flex items-center justify-between rounded-lg border p-3"
                            >
                              <span className="font-medium">{subtitle.langName}</span>
                              <div className="flex gap-2">
                                <Button variant="outline" size="sm">
                                  查看
                                </Button>
                                <Button size="sm">
                                  <Download className="mr-2 h-4 w-4" />
                                  下载 SRT
                                </Button>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  <Button variant="outline" className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    一键下载所有原始字幕 (.zip)
                  </Button>
                </div>
              </div>

              {/* 多语言字幕制作面板 (占位) */}
              <div className="rounded-lg border p-4">
                <h3 className="text-lg font-semibold">多语言字幕制作</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  此功能将在后续任务中实现。
                </p>
              </div>
            </>
          ) : (
            <div className="rounded-lg border p-8 text-center">
              <p className="text-muted-foreground">此视频没有任何可用字幕</p>
            </div>
          )}
        </div>
      </TabsContent>
    </Tabs>
  );
}