{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/mocks/data/video-page-data.ts"], "sourcesContent": ["import { VideoPageData } from '@/lib/types/definitions';\r\n\r\n/**\r\n * 单次下载页的模拟数据\r\n * videoId: dQw4w9WgXcQ (<PERSON> - Never Gonna Give You Up)\r\n */\r\nexport const mockVideoPageData: VideoPageData = {\r\n  id: 'dQw4w9WgXcQ',\r\n  title: '<PERSON> - Never Gonna Give You Up (Official Music Video)',\r\n  description:\r\n    'The official video for “Never Gonna Give You Up” by <PERSON>...\\n' +\r\n    'Listen to <PERSON>: https://RickAs<PERSON>.lnk.to/listenYD\\n' +\r\n    'Subscribe to the official <PERSON> YouTube channel: https://RickAstley.lnk.to/subscribeYD\\n\\n' +\r\n    'Follow <PERSON>:\\n' +\r\n    'Facebook: https://RickAstley.lnk.to/followFI\\n' +\r\n    'Twitter: https://RickAstley.lnk.to/followTI\\n' +\r\n    'Instagram: https://RickAstley.lnk.to/followII\\n' +\r\n    'Website: https://RickAstley.lnk.to/followWI\\n' +\r\n    'Spotify: https://RickAstley.lnk.to/followSI\\n' +\r\n    'YouTube: https://RickAstley.lnk.to/subscribeYD',\r\n  duration: 212,\r\n  channelName: 'RickAstleyVEVO',\r\n  channelUrl: 'https://www.youtube.com/user/<PERSON>tleyVEVO',\r\n  uploadDate: '2009-10-25T06:57:33Z',\r\n  viewCount: 1589732984,\r\n  commentCount: 2345678,\r\n  commentsDisabled: false,\r\n  thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',\r\n  thumbnails: [\r\n    { format: 'jpg', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg' },\r\n    { format: 'png', url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.png' }, // 示例, png 通常不存在\r\n  ],\r\n  videoStreams: [\r\n    { qualityLabel: '1080p HD', resolution: '1920x1080', fps: 60, fileSize: 98765432, format: 'MP4', downloadId: 'v1' },\r\n    { qualityLabel: '720p', resolution: '1280x720', fps: 30, fileSize: 54321098, format: 'MP4', downloadId: 'v2' },\r\n    { qualityLabel: '480p', resolution: '854x480', fps: 30, fileSize: 23456789, format: 'WebM', downloadId: 'v3' },\r\n  ],\r\n  audioStreams: [\r\n    { qualityLabel: 'High', resolution: '', fps: 0, fileSize: 5678901, format: 'M4A', bitrate: 128, downloadId: 'a1' },\r\n    { qualityLabel: 'Medium', resolution: '', fps: 0, fileSize: 3456789, format: 'WebM', bitrate: 160, downloadId: 'a2' },\r\n    { qualityLabel: 'MP3 320kbps', resolution: '', fps: 0, fileSize: 7890123, format: 'MP3', bitrate: 320, downloadId: 'a3-320' },\r\n    { qualityLabel: 'MP3 192kbps', resolution: '', fps: 0, fileSize: 4567890, format: 'MP3', bitrate: 192, downloadId: 'a3-192' },\r\n  ],\r\n  subtitles: [\r\n    { langCode: 'en', langName: 'English', isAutoGenerated: false },\r\n    { langCode: 'es', langName: 'Spanish', isAutoGenerated: false },\r\n    { langCode: 'fr', langName: 'French', isAutoGenerated: true },\r\n    { langCode: 'de', langName: 'German', isAutoGenerated: true },\r\n    { langCode: 'zh-Hans', langName: 'Chinese (Simplified)', isAutoGenerated: true },\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AAMO,MAAM,oBAAmC;IAC9C,IAAI;IACJ,OAAO;IACP,aACE,yEACA,gEACA,qGACA,0BACA,mDACA,kDACA,oDACA,kDACA,kDACA;IACF,UAAU;IACV,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,cAAc;IACd,YAAY;QACV;YAAE,QAAQ;YAAO,KAAK;QAAuD;QAC7E;YAAE,QAAQ;YAAO,KAAK;QAAuD;KAC9E;IACD,cAAc;QACZ;YAAE,cAAc;YAAY,YAAY;YAAa,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAO,YAAY;QAAK;QAClH;YAAE,cAAc;YAAQ,YAAY;YAAY,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAO,YAAY;QAAK;QAC7G;YAAE,cAAc;YAAQ,YAAY;YAAW,KAAK;YAAI,UAAU;YAAU,QAAQ;YAAQ,YAAY;QAAK;KAC9G;IACD,cAAc;QACZ;YAAE,cAAc;YAAQ,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAK;QACjH;YAAE,cAAc;YAAU,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAQ,SAAS;YAAK,YAAY;QAAK;QACpH;YAAE,cAAc;YAAe,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAS;QAC5H;YAAE,cAAc;YAAe,YAAY;YAAI,KAAK;YAAG,UAAU;YAAS,QAAQ;YAAO,SAAS;YAAK,YAAY;QAAS;KAC7H;IACD,WAAW;QACT;YAAE,UAAU;YAAM,UAAU;YAAW,iBAAiB;QAAM;QAC9D;YAAE,UAAU;YAAM,UAAU;YAAW,iBAAiB;QAAM;QAC9D;YAAE,UAAU;YAAM,UAAU;YAAU,iBAAiB;QAAK;QAC5D;YAAE,UAAU;YAAM,UAAU;YAAU,iBAAiB;QAAK;QAC5D;YAAE,UAAU;YAAW,UAAU;YAAwB,iBAAiB;QAAK;KAChF;AACH", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E5%AD%A6%E4%B9%A0/YTDownloader/src/web-client/app/api/youtube/metadata/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockVideoPageData } from '@/mocks/data/video-page-data';\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const videoId = searchParams.get('videoId');\n\n  // 模拟网络延迟\n  await new Promise(resolve => setTimeout(resolve, 500));\n\n  console.log(`🔶 API Route intercepted: /api/youtube/metadata?videoId=${videoId}`);\n\n  if (videoId === 'error') {\n    return NextResponse.json(\n      { error: 'Video not found' },\n      { status: 404 }\n    );\n  }\n\n  if (videoId === 'dQw4w9WgXcQ') {\n    return NextResponse.json(mockVideoPageData);\n  }\n  \n  // 对于任何其他 videoId，也返回成功的数据\n  return NextResponse.json({\n    ...mockVideoPageData,\n    id: videoId,\n    title: `(Mocked) Video Title for ${videoId}`,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,SAAS;IACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,QAAQ,GAAG,CAAC,CAAC,wDAAwD,EAAE,SAAS;IAEhF,IAAI,YAAY,SAAS;QACvB,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkB,GAC3B;YAAE,QAAQ;QAAI;IAElB;IAEA,IAAI,YAAY,eAAe;QAC7B,OAAO,gJAAY,CAAC,IAAI,CAAC,6JAAiB;IAC5C;IAEA,0BAA0B;IAC1B,OAAO,gJAAY,CAAC,IAAI,CAAC;QACvB,GAAG,6JAAiB;QACpB,IAAI;QACJ,OAAO,CAAC,yBAAyB,EAAE,SAAS;IAC9C;AACF", "debugId": null}}]}