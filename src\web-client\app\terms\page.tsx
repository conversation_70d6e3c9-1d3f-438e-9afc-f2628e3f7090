import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata = {
  title: "服务条款 - YTDownloader",
  description: "YTDownloader 服务条款和使用协议",
};

export default function TermsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      
      <main className="flex-grow bg-muted/20">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="mb-8 text-center">
            <h1 className="text-4xl font-bold">服务条款</h1>
            <p className="mt-4 text-lg text-muted-foreground">
              最后更新时间：{new Date().toLocaleDateString('zh-CN')}
            </p>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>1. 服务说明</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  YTDownloader是一个专业的YouTube内容下载平台，
                  为用户提供视频、音频、字幕等内容的下载服务。
                </p>
                <p>
                  本服务支持单次下载和批量处理，并提供在线剪辑与格式转换功能。
                  我们致力于为用户提供高质量、高效率的下载体验。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>2. 用户责任</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>2.1 合法使用：</strong>
                  用户承诺仅将本服务用于个人学习、研究等非商业用途。
                  严禁将下载的内容用于任何商业目的或侵犯他人版权的行为。
                </p>
                <p>
                  <strong>2.2 版权责任：</strong>
                  用户需自行承担因使用本服务而产生的版权风险。
                  本服务不对用户下载的内容承担任何版权责任。
                </p>
                <p>
                  <strong>2.3 禁止行为：</strong>
                  用户不得利用本服务进行任何违法违规活动，包括但不限于：
                  传播违法内容、侵犯他人权益、恶意攻击服务器等。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>3. 服务限制</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>3.1 免费版限制：</strong>
                  免费用户可下载最高1080p分辨率视频、192kbps音频，
                  批量处理最多20个视频，评论下载限100条。
                </p>
                <p>
                  <strong>3.2 专业版服务：</strong>
                  付费用户可享受更高质量下载、更大批量处理规模等增值服务。
                </p>
                <p>
                  <strong>3.3 技术限制：</strong>
                  本服务无法下载私有视频、受年龄限制的内容或需要登录才能观看的视频。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>4. 隐私保护</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  我们重视用户隐私，严格按照《隐私政策》处理用户数据。
                  详细信息请参阅我们的隐私政策页面。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>5. 免责声明</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <strong>5.1 服务可用性：</strong>
                  本服务力求保持稳定运行，但不保证服务的绝对可用性。
                  因网络故障、维护升级等原因导致的服务中断，本服务不承担责任。
                </p>
                <p>
                  <strong>5.2 内容准确性：</strong>
                  本服务不对下载内容的准确性、完整性或适用性作出保证。
                </p>
                <p>
                  <strong>5.3 第三方风险：</strong>
                  本服务不对因使用第三方平台（如YouTube）政策变更而导致的服务影响承担责任。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>6. 条款变更</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  本服务保留随时修改本服务条款的权利。
                  条款变更后，我们将在网站上发布更新通知。
                  用户继续使用本服务即表示同意修改后的条款。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>7. 联系我们</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  如果您对本服务条款有任何疑问，请通过以下方式联系我们：
                </p>
                <ul className="list-disc list-inside space-y-1">
                  <li>邮箱：<EMAIL></li>
                  <li>在线客服：通过网站右下角客服窗口</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
